# -*- coding: utf-8 -*-
"""
深度组显示效果示例
展示QLabel在QFrame中居中显示，并设置不同颜色背景
"""

# 标准库
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 第三方库
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QGroupBox, QFrame, QLabel, QPushButton
)
from PySide6.QtCore import Qt

# 本地库
from common.style.theme_manager import theme_manager
from common.style.style_manager import style_manager
from common.style.responsive_layout import responsive_layout
from common.i18n.i18n_manager import i18n_manager, tr


class DepthGroupExample(QMainWindow):
    """深度组显示示例"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("深度组显示效果示例")
        self.setGeometry(100, 100, 1000, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建主题切换按钮
        button_layout = QHBoxLayout()
        self.light_btn = QPushButton("浅色主题")
        self.dark_btn = QPushButton("深色主题")
        self.light_btn.clicked.connect(lambda: self.switch_theme("light"))
        self.dark_btn.clicked.connect(lambda: self.switch_theme("dark"))
        button_layout.addWidget(self.light_btn)
        button_layout.addWidget(self.dark_btn)
        button_layout.addStretch()
        main_layout.addLayout(button_layout)
        
        # 创建深度组
        depth_group = self.create_depth_group()
        main_layout.addWidget(depth_group)
        
        # 应用样式
        self.setStyleSheet(style_manager.get_complete_style())
    
    def create_depth_group(self) -> QGroupBox:
        """创建深度显示组 - 修改后的版本"""
        group = QGroupBox(tr("depth_title", "深度"))
        layout = QHBoxLayout(group)
        layout.setSpacing(responsive_layout.get_spacing())

        # 深度数值显示 - 蓝色背景
        self.depth_box = QFrame()
        self.depth_box.setProperty("cardStyle", "depth")  # 设置自定义属性用于样式
        depth_layout = QVBoxLayout(self.depth_box)
        depth_layout.setContentsMargins(responsive_layout.get_margin(),
                                       responsive_layout.get_margin(),
                                       responsive_layout.get_margin(),
                                       responsive_layout.get_margin())
        # 深度标题
        self.depth_label = QLabel(tr("depth_title", "深度"))
        self.depth_label.setProperty("labelType", "title")
        self.depth_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        depth_layout.addWidget(self.depth_label)
        # 深度数值
        self.depth_label_value = QLabel("125.6 m")
        self.depth_label_value.setProperty("labelType", "large")
        self.depth_label_value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        depth_layout.addWidget(self.depth_label_value)
        layout.addWidget(self.depth_box)

        # 当前状态 - 绿色背景
        self.current_status_box = QFrame()
        self.current_status_box.setProperty("cardStyle", "status")  # 设置自定义属性用于样式
        status_layout = QVBoxLayout(self.current_status_box)
        status_layout.setContentsMargins(responsive_layout.get_margin(),
                                        responsive_layout.get_margin(),
                                        responsive_layout.get_margin(),
                                        responsive_layout.get_margin())
        # 状态标题
        self.current_status_label = QLabel(tr("current_status", "实时状态"))
        self.current_status_label.setProperty("labelType", "title")
        self.current_status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_layout.addWidget(self.current_status_label)
        # 状态数值
        self.current_status_value = QLabel("正在下降")
        self.current_status_value.setProperty("labelType", "large")
        self.current_status_value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_layout.addWidget(self.current_status_value)
        layout.addWidget(self.current_status_box)

        # 下一步动作 - 橙色背景
        self.next_action_box = QFrame()
        self.next_action_box.setProperty("cardStyle", "action")  # 设置自定义属性用于样式
        action_layout = QVBoxLayout(self.next_action_box)
        action_layout.setContentsMargins(responsive_layout.get_margin(),
                                        responsive_layout.get_margin(),
                                        responsive_layout.get_margin(),
                                        responsive_layout.get_margin())
        # 动作标题
        self.next_action_label = QLabel(tr("next_action", "下一步动作"))
        self.next_action_label.setProperty("labelType", "title")
        self.next_action_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        action_layout.addWidget(self.next_action_label)
        # 动作数值
        self.next_action_value = QLabel("继续下降至150m")
        self.next_action_value.setProperty("labelType", "large")
        self.next_action_value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        action_layout.addWidget(self.next_action_value)
        layout.addWidget(self.next_action_box)

        return group
    
    def switch_theme(self, theme_name):
        """切换主题"""
        theme_manager.set_theme(theme_name)
        self.setStyleSheet(style_manager.get_complete_style())


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 初始化国际化
    i18n_manager.set_language("zh_CN")
    
    # 设置主题
    theme_manager.set_theme("light")
    
    # 创建示例窗口
    window = DepthGroupExample()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
