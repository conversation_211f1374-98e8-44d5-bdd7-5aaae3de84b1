# -*- coding: utf-8 -*-
"""
测试深度组显示效果
"""

# 标准库
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 第三方库
from PySide6.QtWidgets import QApp<PERSON>, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton
from PySide6.QtCore import Qt

# 本地库
from src.worker_system.real_time.view.realtimeview import RealtimeView
from common.style.theme_manager import theme_manager
from common.style.style_manager import style_manager
from common.i18n.i18n_manager import i18n_manager


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("深度组显示测试")
        self.setGeometry(100, 100, 900, 300)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)

        # 创建主题切换按钮
        button_layout = QHBoxLayout()
        self.light_btn = QPushButton("浅色主题")
        self.dark_btn = QPushButton("深色主题")
        self.light_btn.clicked.connect(lambda: self.switch_theme("light"))
        self.dark_btn.clicked.connect(lambda: self.switch_theme("dark"))
        button_layout.addWidget(self.light_btn)
        button_layout.addWidget(self.dark_btn)
        button_layout.addStretch()
        main_layout.addLayout(button_layout)

        # 创建实时视图实例（仅用于测试深度组）
        self.realtime_view = RealtimeView()

        # 创建深度组
        depth_group = self.realtime_view._create_depth_group()
        main_layout.addWidget(depth_group)

        # 应用样式
        self.setStyleSheet(style_manager.get_complete_style())

    def switch_theme(self, theme_name):
        """切换主题"""
        theme_manager.set_theme(theme_name)
        self.setStyleSheet(style_manager.get_complete_style())


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 初始化国际化
    i18n_manager.set_language("zh_CN")
    
    # 设置主题
    theme_manager.set_theme("light")  # 可以改为 "dark" 测试深色主题
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
