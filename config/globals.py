# #!/usr/bin/python3
# -*- coding: UTF-8 -*-
'''
<AUTHOR>   lx <<EMAIL>> <<EMAIL>>
@Time    :   2025/07/27 10:43:00
@File    :   globals.py
@Desc    :   全局变量
'''

from collections import deque  

EQUIPMENT = []
'''连接设备列表'''
FILESIZE = None
'''更新文件大小'''
WINCHSTATE = False
'''绞车运行状态'''

WT1_X = deque(maxlen=200)
WT1_Y = deque(maxlen=200)
WT1_Z = deque(maxlen=200)
WT1_SUM = 0
'''WT1数据量'''

WT2_X = deque(maxlen=200)
WT2_Y = deque(maxlen=200)
WT2_Z = deque(maxlen=200)
WT2_SUM = 0
'''WT2数据量'''

WT3_X = deque(maxlen=200)
WT3_Y = deque(maxlen=200)
WT3_Z = deque(maxlen=200)
WT3_SUM = 0
'''WT3数据量'''

WT4_X = deque(maxlen=200)
WT4_Y = deque(maxlen=200)
WT4_Z = deque(maxlen=200)
WT4_SUM = 0
'''WT4数据量'''

WT5_X = deque(maxlen=200)
WT5_Y = deque(maxlen=200)
WT5_Z = deque(maxlen=200)
WT5_SUM = 0
'''WT5数据量'''

WT6_X = deque(maxlen=200)
WT6_Y = deque(maxlen=200)
WT6_Z = deque(maxlen=200)
WT6_SUM = 0
'''WT6数据量'''

TENSION = deque(maxlen=200)
'''张力数据队列'''
DEPTH = deque(maxlen=200)
'''深度数据队列'''
SPEED = deque(maxlen=200)
'''速度数据队列'''