# -*- coding: utf-8 -*-
"""
扭矩滑块测试
展示增大扭矩和减小扭矩滑块的效果
"""

# 标准库
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 第三方库
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QGroupBox, QPushButton
)
from PySide6.QtCore import Qt

# 本地库
from src.shared_ui.modern_slider import TorqueSlider
from common.style.theme_manager import theme_manager
from common.style.style_manager import style_manager
from common.style.responsive_layout import responsive_layout
from common.i18n.i18n_manager import i18n_manager, tr


class TorqueSliderTest(QMainWindow):
    """扭矩滑块测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("扭矩滑块测试")
        self.setGeometry(100, 100, 800, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建主题切换按钮
        button_layout = QHBoxLayout()
        self.light_btn = QPushButton("浅色主题")
        self.dark_btn = QPushButton("深色主题")
        self.light_btn.clicked.connect(lambda: self.switch_theme("light"))
        self.dark_btn.clicked.connect(lambda: self.switch_theme("dark"))
        button_layout.addWidget(self.light_btn)
        button_layout.addWidget(self.dark_btn)
        button_layout.addStretch()
        main_layout.addLayout(button_layout)
        
        # 创建扭矩控制组
        torque_group = self.create_torque_control_group()
        main_layout.addWidget(torque_group)
        
        # 应用样式
        self.setStyleSheet(style_manager.get_complete_style())
    
    def create_torque_control_group(self) -> QGroupBox:
        """创建扭矩控制组"""
        group = QGroupBox(tr("control_title", "扭矩控制"))
        layout = QVBoxLayout(group)
        layout.setSpacing(responsive_layout.get_spacing())
        
        # 增大扭矩滑块
        self.increase_torque_slider = TorqueSlider(tr("increase_torque", "增大扭矩"))
        self.increase_torque_slider.valueChanged.connect(self.on_increase_torque_changed)
        layout.addWidget(self.increase_torque_slider)
        
        # 减小扭矩滑块
        self.decrease_torque_slider = TorqueSlider(tr("decrease_torque", "减小扭矩"))
        self.decrease_torque_slider.valueChanged.connect(self.on_decrease_torque_changed)
        layout.addWidget(self.decrease_torque_slider)
        
        return group
    
    def on_increase_torque_changed(self, value: int):
        """增大扭矩滑块值变化"""
        print(f"增大扭矩: {value}%")
        # 这里可以添加实际的扭矩控制逻辑
    
    def on_decrease_torque_changed(self, value: int):
        """减小扭矩滑块值变化"""
        print(f"减小扭矩: {value}%")
        # 这里可以添加实际的扭矩控制逻辑
    
    def switch_theme(self, theme_name):
        """切换主题"""
        theme_manager.set_theme(theme_name)
        self.setStyleSheet(style_manager.get_complete_style())


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 初始化国际化
    i18n_manager.set_language("zh_CN")
    
    # 设置主题
    theme_manager.set_theme("light")
    
    # 创建测试窗口
    window = TorqueSliderTest()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
