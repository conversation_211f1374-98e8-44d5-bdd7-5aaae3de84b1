#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时监控界面测试脚本
用于独立测试实时监控功能
"""

# 标准库
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 第三方库
from PySide6.QtWidgets import QApplication

# 本地库
from src.worker_system.real_time.realtimehander import create_realtime_app
from common.style.theme_manager import theme_manager
from common.i18n.i18n_manager import i18n_manager


def main():
    """主函数"""
    print("启动实时监控界面测试...")
    
    # 创建应用
    app, handler = create_realtime_app()
    
    # 设置样式
    app.setStyle('Fusion')
    
    # 可选：切换到深色主题进行测试
    # theme_manager.set_theme("dark")
    
    # 可选：切换语言进行测试
    # i18n_manager.set_language("en_US")
    
    print("界面已启动，可以进行以下测试：")
    print("1. 点击'连接绞车'按钮测试设备连接")
    print("2. 连接后可以测试'上提'和'下放'操作")
    print("3. 测试'紧急停车'功能")
    print("4. 观察实时数据更新和图表显示")
    print("5. 调整窗口大小测试响应式布局")
    
    # 显示界面
    handler.show_view()
    
    # 运行应用
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
