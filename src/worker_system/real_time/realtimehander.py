# -*- coding: utf-8 -*-
"""
实时监控处理器
负责协调视图和控制器，处理槽函数绑定和界面启动
"""

# 标准库
import sys

# 第三方库
from PySide6.QtWidgets import QApplication, QWidget
from PySide6.QtCore import QTimer

# 本地库
from src.worker_system.real_time.view.realtimeview import RealtimeView
from src.worker_system.real_time.controller.realtime_controller import RealtimeController
from common.style.theme_manager import theme_manager
from common.style.responsive_layout import responsive_layout
from common.i18n.i18n_manager import i18n_manager, tr


class RealtimeHandler(QWidget):
    """实时监控处理器"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # 初始化组件
        self._init_components()

        # 设置窗口属性
        self._setup_window()

        # 连接信号槽
        self._connect_signals()

        # 启动初始化定时器
        QTimer.singleShot(100, self._post_init)

    def _init_components(self):
        """初始化组件"""
        # 创建视图
        self.view = RealtimeView(self)

        # 创建控制器
        self.controller = RealtimeController(self.view)

    def _setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle(tr("operator_title", "测井仪器井下运行状态智能诊断系统 - 绞车工工作台"))

        # 设置窗口尺寸
        self.resize(1600, 1200)

        # 居中显示
        self._center_window()

    def _center_window(self):
        """窗口居中显示"""
        from PySide6.QtGui import QGuiApplication

        screen = QGuiApplication.primaryScreen()
        if screen:
            screen_geometry = screen.availableGeometry()
            window_geometry = self.frameGeometry()
            center_point = screen_geometry.center()
            window_geometry.moveCenter(center_point)
            self.move(window_geometry.topLeft())

    def _connect_signals(self):
        """连接信号槽"""
        # 主题管理器信号
        theme_manager.theme_changed.connect(self._on_theme_changed)

        # 响应式布局信号
        responsive_layout.screen_size_changed.connect(self._on_screen_size_changed)

        # 国际化信号
        i18n_manager.language_changed.connect(self._on_language_changed)

    def _post_init(self):
        """后初始化处理"""
        # 更新屏幕尺寸
        responsive_layout.update_screen_size(self.width(), self.height())

        # 显示视图
        self.view.show()

    def _on_theme_changed(self, theme_name: str):
        """主题变化处理"""
        print(f"主题已切换到: {theme_name}")

    def _on_screen_size_changed(self, screen_size):
        """屏幕尺寸变化处理"""
        print(f"屏幕尺寸已变化: {screen_size}")

    def _on_language_changed(self, language_code: str):
        """语言变化处理"""
        print(f"语言已切换到: {language_code}")
        # 可以在这里重新设置窗口标题等文本
        self.setWindowTitle(tr("operator_title", "测井仪器井下运行状态智能诊断系统 - 绞车工工作台"))

    def resizeEvent(self, event):
        """窗口大小变化事件"""
        super().resizeEvent(event)
        # 更新响应式布局
        responsive_layout.update_screen_size(event.size().width(), event.size().height())

    def show_view(self):
        """显示视图"""
        self.view.show()

    def hide_view(self):
        """隐藏视图"""
        self.view.hide()

    def get_view(self):
        """获取视图实例"""
        return self.view

    def get_controller(self):
        """获取控制器实例"""
        return self.controller


def create_realtime_app():
    """创建实时监控应用"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    # 设置应用程序属性
    app.setApplicationName(tr("system_name", "测井仪器井下运行状态智能诊断系统"))
    app.setApplicationVersion("2.1")
    app.setOrganizationName("SmartWinch")

    # 创建处理器
    handler = RealtimeHandler()

    return app, handler


def main():
    """主函数 - 独立运行实时监控模块"""
    app, handler = create_realtime_app()

    # 显示界面
    handler.show_view()

    # 运行应用
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
