
# -*- coding: utf-8 -*-
"""
实时监控视图
负责显示实时数据和用户交互界面
"""

# 标准库
import sys
import os
import math
from typing import Dict, List, Optional

# 第三方库
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QFrame, QGridLayout, QSplitter, QScrollArea, QGroupBox
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont
import numpy as np

# 本地库
from src.shared_ui.circular_gauge import CircularGauge
from src.shared_ui.modern_chart import Modern<PERSON>hart
from src.shared_ui.modern_button import ModernButton, ButtonType, ButtonSize
from src.shared_ui.modern_slider import ModernSlider, TorqueSlider
from src.shared_ui.status_indicator import StatusIndicator, StatusType, AlarmIndicator
from common.style.theme_manager import theme_manager
from common.style.responsive_layout import responsive_layout
from common.style.style_manager import style_manager
from common.i18n.i18n_manager import tr
class RealtimeView(QMainWindow):
    """实时监控视图"""

    # 信号定义
    connect_winch_clicked = Signal()
    emergency_stop_clicked = Signal()
    lift_up_clicked = Signal()
    lower_down_clicked = Signal()
    increase_torque_changed = Signal(int)
    decrease_torque_changed = Signal(int)

    def __init__(self, parent=None):
        super().__init__(parent)

        # 数据存储
        self.gauges = {}
        self.charts = {}
        self.status_indicators = {}

        # 初始化UI
        self._init_ui()
        self._setup_connections()

        # 连接主题变化信号
        theme_manager.theme_changed.connect(self._update_theme)
        responsive_layout.screen_size_changed.connect(self._update_layout)

        # 应用样式
        self._update_theme()

    def _init_ui(self):
        # 单前导下划线 _xxx  内部使用标识符（约定俗成）

        """初始化UI"""
        self.setWindowTitle(tr("operator_title", "测井仪器井下运行状态智能诊断系统 - 绞车工工作台"))
        self.setGeometry(100, 100, 1600, 1200) # 设置窗口的几何大小

        # 主容器
        central_widget = QWidget()
        self.setCentralWidget(central_widget) # 设置为中间部件

        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(responsive_layout.get_margin(),
                                     responsive_layout.get_margin(),
                                     responsive_layout.get_margin(),
                                     responsive_layout.get_margin())  # 主要设置控件的内边距
        main_layout.setSpacing(responsive_layout.get_spacing())

        # 创建左侧面板（仪表盘和控制）
        left_panel = self._create_left_panel()
        main_layout.addWidget(left_panel, 1)

        # 创建右侧面板（图表和状态）
        right_panel = self._create_right_panel()
        main_layout.addWidget(right_panel, 2)

    def _create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(responsive_layout.get_spacing())

        # 仪表盘区域
        gauges_group = self._create_gauges_group()
        layout.addWidget(gauges_group)

        # 深度显示区域
        depth_group = self._create_depth_group()
        layout.addWidget(depth_group)

        # 状态指示区域
        status_group = self._create_status_group()
        layout.addWidget(status_group)

        # 控制按钮区域
        control_group = self._create_control_group()
        layout.addWidget(control_group)

        layout.addStretch() 
        return panel

    def _create_gauges_group(self) -> QGroupBox:
        """创建仪表盘组"""
        group = QGroupBox(tr("gauges_title", "实时参数"))
        layout = QGridLayout(group)
        layout.setSpacing(responsive_layout.get_spacing())

        # 创建四个仪表盘
        gauge_configs = [
            ("speed", tr("speed_gauge", "速度"), 0, 100, tr("speed_unit", " m/h")),
            ("acceleration", tr("acceleration_gauge", "加速度"), -10, 10, tr("acceleration_unit", " m/s²")),
            ("tension", tr("tension_gauge", "张力"), 0, 50, tr("tension_unit", " kN")),
            ("diff_tension", tr("diff_tension_gauge", "差分张力"), -20, 20, tr("tension_unit", " kN"))
        ]

        for i, (key, title, min_val, max_val, unit) in enumerate(gauge_configs):
            gauge = CircularGauge(title, min_val, max_val, unit)
            self.gauges[key] = gauge

            row = i // 2
            col = i % 2
            layout.addWidget(gauge, row, col)

        return group

    def _create_depth_group(self) -> QGroupBox:
        """创建深度显示组"""
        group = QGroupBox(tr("depth_title", "状态"))
        layout = QHBoxLayout(group)
        layout.setSpacing(responsive_layout.get_spacing())

        # 深度数值显示
        self.depth_box = QFrame()
        self.depth_box.setProperty("cardStyle", "depth")  # 设置自定义属性用于样式
        depth_layout = QVBoxLayout(self.depth_box)

        # 深度标题
        self.depth_label = QLabel(tr("depth_title", "深度"))
        self.depth_label.setProperty("labelType", "title")
        self.depth_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        depth_layout.addWidget(self.depth_label)
        # 深度数值
        self.depth_label_value = QLabel("0.0 m")
        self.depth_label_value.setProperty("labelType", "large") # 设置自定义属性用于样式 ，调用setProperty 方法，为该控件设置一个名为 labelType 的属性，并将其值设置为 "large"
        self.depth_label_value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        depth_layout.addWidget(self.depth_label_value)
        layout.addWidget(self.depth_box)

        # 当前状态
        self.current_status_box = QFrame()
        self.current_status_box.setProperty("cardStyle", "status")  # 设置自定义属性用于样式
        status_layout = QVBoxLayout(self.current_status_box)

        # 状态标题
        self.current_status_label = QLabel(tr("current_status", "实时状态"))
        self.current_status_label.setProperty("labelType", "title")
        self.current_status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_layout.addWidget(self.current_status_label)
        # 状态数值
        self.current_status_value = QLabel("正常运行")
        self.current_status_value.setProperty("labelType", "large")
        self.current_status_value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_layout.addWidget(self.current_status_value)
        layout.addWidget(self.current_status_box)

        # 下一步动作
        self.next_action_box = QFrame()
        self.next_action_box.setProperty("cardStyle", "action")  # 设置自定义属性用于样式
        action_layout = QVBoxLayout(self.next_action_box)

        # 动作标题
        self.next_action_label = QLabel(tr("next_action", "下一步动作"))
        self.next_action_label.setProperty("labelType", "title")
        self.next_action_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        action_layout.addWidget(self.next_action_label)
        # 动作数值
        self.next_action_value = QLabel("继续下降")
        self.next_action_value.setProperty("labelType", "large")
        self.next_action_value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        action_layout.addWidget(self.next_action_value)
        layout.addWidget(self.next_action_box)

        return group

    def _create_status_group(self) -> QGroupBox:
        """创建状态指示组"""
        group = QGroupBox(tr("status_title", "系统状态"))
        layout = QVBoxLayout(group)
        layout.setSpacing(responsive_layout.get_spacing())

        # 设备状态指示器
        self.device_status = StatusIndicator(
            tr("device_status", "设备状态"),
            StatusType.OFFLINE,
            tr("device_not_connected", "设备未连接")
        )
        self.status_indicators["device"] = self.device_status
        layout.addWidget(self.device_status)

        # 报警指示器
        self.alarm_indicator = AlarmIndicator(
            tr("alarm_obstacle", "设备遇阻"),
            tr("alarm_time", "03:49:10"),
            tr("alarm_depth", "2427m")
        )
        self.status_indicators["alarm"] = self.alarm_indicator
        layout.addWidget(self.alarm_indicator)

        return group


    def _create_control_group(self) -> QGroupBox:
        """创建控制按钮组"""
        group = QGroupBox(tr("control_title", "设备控制"))
        layout = QVBoxLayout(group)
        layout.setSpacing(responsive_layout.get_spacing())

        # 连接绞车按钮
        self.connect_winch_btn = ModernButton(
            tr("controller.connect_winch", "连接绞车"),
            ButtonType.PRIMARY,
            ButtonSize.NORMAL
        )
        layout.addWidget(self.connect_winch_btn)

        # 紧急停车按钮
        self.emergency_stop_btn = ModernButton(
            tr("emergency_stop", "紧急停车"),
            ButtonType.DANGER,
            ButtonSize.NORMAL
        )
        layout.addWidget(self.emergency_stop_btn)

        # 操作按钮布局
        operation_layout = QHBoxLayout()

        # 上提按钮
        self.lift_up_btn = ModernButton(
            tr("lift_up", "上提"),
            ButtonType.SUCCESS,
            ButtonSize.NORMAL
        )
        operation_layout.addWidget(self.lift_up_btn)

        # 下放按钮
        self.lower_down_btn = ModernButton(
            tr("lower_down", "下放"),
            ButtonType.WARNING,
            ButtonSize.NORMAL
        )
        operation_layout.addWidget(self.lower_down_btn)

        layout.addLayout(operation_layout)

        # 扭矩控制滑块区域
        torque_layout = QVBoxLayout()

        # 增大扭矩滑块
        self.increase_torque_slider = TorqueSlider(tr("increase_torque", "增大扭矩"))
        self.increase_torque_slider.valueChanged.connect(self._on_increase_torque_changed)
        torque_layout.addWidget(self.increase_torque_slider)

        # 减小扭矩滑块
        self.decrease_torque_slider = TorqueSlider(tr("decrease_torque", "减小扭矩"))
        self.decrease_torque_slider.valueChanged.connect(self._on_decrease_torque_changed)
        torque_layout.addWidget(self.decrease_torque_slider)

        layout.addLayout(torque_layout)

        return group

    
    def _create_right_panel(self) -> QWidget:
        """创建右侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(responsive_layout.get_spacing())

        # 图表区域
        charts_group = self._create_charts_group()
        layout.addWidget(charts_group)

        return panel

    def _create_charts_group(self) -> QGroupBox:
        """创建图表组"""
        group = QGroupBox(tr("charts_title", "实时曲线"))
        layout = QVBoxLayout(group)
        layout.setSpacing(responsive_layout.get_spacing())

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)

        # 速度和张力曲线（上半部分）
        top_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 速度曲线
        speed_chart = ModernChart(
            tr("speed_curve", "速度曲线"),
            tr("time_axis", "时间"),
            tr("speed_value", "速度值")
        )
        speed_chart.add_curve("speed", theme_manager.get_color("CHART_LINE_1"))
        self.charts["speed"] = speed_chart
        top_splitter.addWidget(speed_chart)

        # 张力曲线
        tension_chart = ModernChart(
            tr("tension_curve", "张力曲线"),
            tr("time_axis", "时间"),
            tr("tension_value", "张力值")
        )
        tension_chart.add_curve("tension", theme_manager.get_color("CHART_LINE_2"))
        self.charts["tension"] = tension_chart
        top_splitter.addWidget(tension_chart)

        splitter.addWidget(top_splitter)

        # 加速度和差分张力曲线（下半部分）
        bottom_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 加速度曲线
        acceleration_chart = ModernChart(
            tr("acceleration_curve", "加速度曲线"),
            tr("time_axis", "时间"),
            tr("acceleration_value", "加速度值")
        )
        acceleration_chart.add_curve("acceleration", theme_manager.get_color("CHART_LINE_3"))
        self.charts["acceleration"] = acceleration_chart
        bottom_splitter.addWidget(acceleration_chart)

        # 差分张力曲线
        diff_tension_chart = ModernChart(
            tr("diff_tension_curve", "差分张力曲线"),
            tr("time_axis", "时间"),
            tr("tension_value", "张力值")
        )
        diff_tension_chart.add_curve("diff_tension", theme_manager.get_color("CHART_LINE_4"))
        self.charts["diff_tension"] = diff_tension_chart
        bottom_splitter.addWidget(diff_tension_chart)

        splitter.addWidget(bottom_splitter)

        # 设置分割比例
        splitter.setSizes([400, 400])
        top_splitter.setSizes([400, 400])
        bottom_splitter.setSizes([400, 400])

        layout.addWidget(splitter)

        return group

    def _setup_connections(self):
        """设置信号连接"""
        # 连接按钮信号
        self.connect_winch_btn.clicked.connect(self.connect_winch_clicked.emit)
        self.emergency_stop_btn.clicked.connect(self.emergency_stop_clicked.emit)
        self.lift_up_btn.clicked.connect(self.lift_up_clicked.emit)
        self.lower_down_btn.clicked.connect(self.lower_down_clicked.emit)

    def _update_theme(self):
        """更新主题"""
        # 应用样式表
        self.setStyleSheet(style_manager.get_complete_style())

    def _update_layout(self, screen_size):
        """更新布局"""
        margin = responsive_layout.get_margin()
        spacing = responsive_layout.get_spacing()

        # 更新主布局
        main_layout = self.centralWidget().layout()
        main_layout.setContentsMargins(margin, margin, margin, margin)
        main_layout.setSpacing(spacing)

    def _on_increase_torque_changed(self, value: int):
        """增大扭矩滑块值变化"""
        print(f"增大扭矩: {value}%")
        self.increase_torque_changed.emit(value)

    def _on_decrease_torque_changed(self, value: int):
        """减小扭矩滑块值变化"""
        print(f"减小扭矩: {value}%")
        self.decrease_torque_changed.emit(value)

    # 数据更新方法，只是有这个方法，这个功能
    def update_gauge_value(self, gauge_name: str, value: float):
        """更新仪表盘数值"""
        if gauge_name in self.gauges:
            self.gauges[gauge_name].setValue(value)

    def update_chart_data(self, chart_name: str, x: float, y: float):
        """更新图表数据"""
        if chart_name in self.charts:
            curve_name = chart_name  # 使用图表名作为曲线名
            self.charts[chart_name].update_curve_data(curve_name, x, y)

    def update_depth(self, depth: float):
        """更新深度显示"""
        self.depth_label_value.setText(f"{depth:.1f} m")

    def update_status(self, status_name: str, status_type: StatusType, message: str = ""):
        """更新状态指示器"""
        if status_name in self.status_indicators:
            self.status_indicators[status_name].set_status(status_type, message)

    def set_button_enabled(self, button_name: str, enabled: bool):
        """设置按钮启用状态"""
        button_map = {
            "connect_winch": self.connect_winch_btn,
            "emergency_stop": self.emergency_stop_btn,
            "lift_up": self.lift_up_btn,
            "lower_down": self.lower_down_btn
        }

        if button_name in button_map:
            button_map[button_name].setEnabled(enabled)

    def set_button_text(self, button_name: str, text: str):
        """设置按钮文本"""
        button_map = {
            "connect_winch": self.connect_winch_btn,
            "emergency_stop": self.emergency_stop_btn,
            "lift_up": self.lift_up_btn,
            "lower_down": self.lower_down_btn
        }

        if button_name in button_map:
            button_map[button_name].set_text(text)