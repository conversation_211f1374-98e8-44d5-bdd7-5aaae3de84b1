# -*- coding: utf-8 -*-
"""
实时监控控制器
负责处理业务逻辑、数据处理和设备控制
"""

# 标准库
import time
import random

# 第三方库
import numpy as np
from PySide6.QtCore import QObject, QTimer, Signal
from PySide6.QtWidgets import QMessageBox

# 本地库
from src.shared_ui.status_indicator import StatusType
from common.i18n.i18n_manager import tr


class RealtimeController(QObject):
    """实时监控控制器"""
    
    # 数据更新信号
    data_updated = Signal(dict)  # 数据字典
    status_changed = Signal(str, StatusType, str)  # 状态名, 状态类型, 消息
    
    def __init__(self, view=None):
        super().__init__()
        self.view = view
        
        # 设备连接状态
        self.is_connected = False
        self.is_running = False
        
        # 数据存储
        self.current_data = {
            'speed': 0.0,
            'acceleration': 0.0,
            'tension': 0.0,
            'diff_tension': 0.0,
            'depth': 0.0,
            'timestamp': 0.0
        }
        
        # 模拟数据生成器
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self._generate_mock_data)
        
        # 初始化连接
        if self.view:
            self._connect_view_signals()
    
    def _connect_view_signals(self):
        """连接视图信号"""
        self.view.connect_winch_clicked.connect(self.toggle_connection)
        self.view.emergency_stop_clicked.connect(self.emergency_stop)
        self.view.lift_up_clicked.connect(self.start_lift_up)
        self.view.lower_down_clicked.connect(self.start_lower_down)
        
        # 连接数据更新信号
        self.data_updated.connect(self._update_view_data)
        self.status_changed.connect(self.view.update_status)
    
    def toggle_connection(self):
        """切换连接状态"""
        if self.is_connected:
            self.disconnect_device()
        else:
            self.connect_device()
    
    def connect_device(self):
        """连接设备"""
        try:
            # 模拟连接过程
            self.view.set_button_text("connect_winch", tr("connecting", "连接中..."))
            self.view.set_button_enabled("connect_winch", False)
            
            # 模拟连接延迟
            QTimer.singleShot(1000, self._on_device_connected)
            
        except Exception as e:
            self._show_error(tr("controller.check_connection", "与设备的通信失败，请检查连接"))
            self.view.set_button_text("connect_winch", tr("controller.connect_winch", "连接绞车"))
            self.view.set_button_enabled("connect_winch", True)
    
    def _on_device_connected(self):
        """设备连接成功处理"""
        self.is_connected = True
        
        # 更新UI状态
        self.view.set_button_text("connect_winch", tr("controller.disconnect_winch", "断开绞车"))
        self.view.set_button_enabled("connect_winch", True)
        self.view.set_button_enabled("emergency_stop", True)
        self.view.set_button_enabled("lift_up", True)
        self.view.set_button_enabled("lower_down", True)
        
        # 更新设备状态
        self.status_changed.emit("device", StatusType.NORMAL, tr("controller.device_running", "设备运行中"))
        
        # 开始数据更新
        self.data_timer.start(100)  # 每100ms更新一次数据
    
    def disconnect_device(self):
        """断开设备连接"""
        self.is_connected = False
        self.is_running = False
        
        # 停止数据更新
        self.data_timer.stop()
        
        # 更新UI状态
        self.view.set_button_text("connect_winch", tr("controller.connect_winch", "连接绞车"))
        self.view.set_button_enabled("emergency_stop", False)
        self.view.set_button_enabled("lift_up", False)
        self.view.set_button_enabled("lower_down", False)
        
        # 更新设备状态
        self.status_changed.emit("device", StatusType.OFFLINE, tr("device_not_connected", "设备未连接"))
    
    def emergency_stop(self):
        """紧急停车"""
        if not self.is_connected:
            return
        
        self.is_running = False
        
        # 更新按钮状态
        self.view.set_button_text("lift_up", tr("lift_up", "上提"))
        self.view.set_button_text("lower_down", tr("lower_down", "下放"))
        self.view.set_button_enabled("lift_up", True)
        self.view.set_button_enabled("lower_down", True)
        
        # 更新状态
        self.status_changed.emit("device", StatusType.WARNING, tr("emergency_stopped", "紧急停车"))
        
        self._show_info(tr("emergency_stop_msg", "设备已紧急停车"))
    
    def start_lift_up(self):
        """开始上提"""
        if not self.is_connected:
            return
        
        if self.is_running:
            # 停止上提
            self.is_running = False
            self.view.set_button_text("lift_up", tr("lift_up", "上提"))
            self.status_changed.emit("device", StatusType.NORMAL, tr("controller.device_stopped", "设备停止"))
        else:
            # 开始上提
            self.is_running = True
            self.view.set_button_text("lift_up", tr("controller.stop_lift", "停止上提"))
            self.view.set_button_text("lower_down", tr("lower_down", "下放"))
            self.status_changed.emit("device", StatusType.INFO, tr("lifting", "正在上提"))
    
    def start_lower_down(self):
        """开始下放"""
        if not self.is_connected:
            return
        
        if self.is_running:
            # 停止下放
            self.is_running = False
            self.view.set_button_text("lower_down", tr("lower_down", "下放"))
            self.status_changed.emit("device", StatusType.NORMAL, tr("controller.device_stopped", "设备停止"))
        else:
            # 开始下放
            self.is_running = True
            self.view.set_button_text("lower_down", tr("controller.stop_lower", "停止下放"))
            self.view.set_button_text("lift_up", tr("lift_up", "上提"))
            self.status_changed.emit("device", StatusType.INFO, tr("lowering", "正在下放"))
    
    def _generate_mock_data(self):
        """生成模拟数据"""
        current_time = time.time()
        
        # 生成模拟数据
        if self.is_running:
            # 运行状态下的数据变化
            self.current_data['speed'] = 20 + random.uniform(-5, 5)
            self.current_data['acceleration'] = random.uniform(-2, 2)
            self.current_data['tension'] = 25 + random.uniform(-3, 3)
            self.current_data['diff_tension'] = random.uniform(-5, 5)
            self.current_data['depth'] += random.uniform(-0.1, 0.1)
        else:
            # 停止状态下的数据
            self.current_data['speed'] = random.uniform(-1, 1)
            self.current_data['acceleration'] = random.uniform(-0.5, 0.5)
            self.current_data['tension'] = 15 + random.uniform(-1, 1)
            self.current_data['diff_tension'] = random.uniform(-1, 1)
        
        self.current_data['timestamp'] = current_time
        
        # 发送数据更新信号
        self.data_updated.emit(self.current_data.copy())
    
    def _update_view_data(self, data):
        """更新视图数据"""
        if not self.view:
            return
        
        # 更新仪表盘
        self.view.update_gauge_value("speed", data['speed'])
        self.view.update_gauge_value("acceleration", data['acceleration'])
        self.view.update_gauge_value("tension", data['tension'])
        self.view.update_gauge_value("diff_tension", data['diff_tension'])
        
        # 更新深度
        self.view.update_depth(data['depth'])
        
        # 更新图表
        timestamp = data['timestamp']
        self.view.update_chart_data("speed", timestamp, data['speed'])
        self.view.update_chart_data("tension", timestamp, data['tension'])
        self.view.update_chart_data("acceleration", timestamp, data['acceleration'])
        self.view.update_chart_data("diff_tension", timestamp, data['diff_tension'])
    
    def _show_error(self, message: str):
        """显示错误消息"""
        if self.view:
            QMessageBox.critical(self.view, tr("error", "错误"), message)
    
    def _show_info(self, message: str):
        """显示信息消息"""
        if self.view:
            QMessageBox.information(self.view, tr("info", "信息"), message)
    
    def get_current_data(self):
        """获取当前数据"""
        return self.current_data.copy()
    
    def is_device_connected(self) -> bool:
        """检查设备是否连接"""
        return self.is_connected
    
    def is_device_running(self) -> bool:
        """检查设备是否运行"""
        return self.is_running
