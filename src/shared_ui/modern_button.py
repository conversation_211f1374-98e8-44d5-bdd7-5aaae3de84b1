# -*- coding: utf-8 -*-
"""
现代化按钮组件
支持多种样式、状态和主题切换
"""

# 标准库
from typing import Optional
from enum import Enum

# 第三方库
from PySide6.QtWidgets import QPushButton, QGraphicsDropShadowEffect
from PySide6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect, Property, Signal
from PySide6.QtGui import QFont, QIcon, QPainter, QPainterPath, QColor

# 本地库
from common.style.theme_manager import theme_manager
from common.style.responsive_layout import responsive_layout
from common.i18n.i18n_manager import tr


class ButtonType(Enum):
    """按钮类型枚举"""
    PRIMARY = "primary"
    SECONDARY = "secondary"
    SUCCESS = "success"
    WARNING = "warning"
    DANGER = "danger"


class ButtonSize(Enum):
    """按钮尺寸枚举"""
    SMALL = "small"
    NORMAL = "normal"
    LARGE = "large"


class ModernButton(QPushButton):
    """现代化按钮组件"""
    
    def __init__(self, text: str = "", button_type: ButtonType = ButtonType.PRIMARY, 
                 button_size: ButtonSize = ButtonSize.NORMAL, icon: QIcon = None, parent=None):
        super().__init__(text, parent)
        
        self.button_type = button_type
        self.button_size = button_size
        self._is_loading = False
        self._original_text = text
        
        # 设置图标
        if icon:
            self.setIcon(icon)
        
        # 初始化样式
        self._init_style()
        
        # 创建阴影效果
        self._create_shadow_effect()
        
        # 连接主题变化信号
        theme_manager.theme_changed.connect(self._update_style)
        responsive_layout.screen_size_changed.connect(self._update_layout)
        
        # 应用初始样式
        self._update_style()
    
    def _init_style(self):
        """初始化样式"""
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
    
    def _create_shadow_effect(self):
        """创建阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(10)
        self.shadow_effect.setOffset(0, 2)
        self.shadow_effect.setColor(QColor(0, 0, 0, 30))
        self.setGraphicsEffect(self.shadow_effect)
    
    def _update_style(self):
        """更新样式"""
        colors = theme_manager.get_colors()
        
        # 获取按钮颜色
        color_map = {
            ButtonType.PRIMARY: (colors['BUTTON_PRIMARY'], colors['BUTTON_PRIMARY_HOVER']),
            ButtonType.SECONDARY: (colors['BUTTON_SECONDARY'], colors['BUTTON_SECONDARY_HOVER']),
            ButtonType.SUCCESS: (colors['BUTTON_SUCCESS'], colors['BUTTON_SUCCESS_HOVER']),
            ButtonType.WARNING: (colors['BUTTON_WARNING'], colors['BUTTON_WARNING_HOVER']),
            ButtonType.DANGER: (colors['BUTTON_DANGER'], colors['BUTTON_DANGER_HOVER'])
        }
        
        bg_color, hover_color = color_map[self.button_type]
        
        # 获取尺寸配置
        size_config = self._get_size_config()
        
        # 设置样式表
        style = f"""
        QPushButton {{
            background-color: {bg_color};
            color: white;
            border: none;
            border-radius: {size_config['border_radius']}px;
            padding: {size_config['padding_v']}px {size_config['padding_h']}px;
            font-size: {size_config['font_size']}px;
            font-weight: bold;
            min-height: {size_config['min_height']}px;
            min-width: {size_config['min_width']}px;
        }}
        
        QPushButton:hover {{
            background-color: {hover_color};
        }}
        
        QPushButton:pressed {{
            background-color: {hover_color};
            margin-top: 1px;
        }}
        
        QPushButton:disabled {{
            background-color: {colors['TEXT_DISABLED']};
            color: {colors['TEXT_SECONDARY']};
        }}
        
        QPushButton:focus {{
            outline: 2px solid {colors['BORDER_FOCUS']};
            outline-offset: 2px;
        }}
        """
        
        self.setStyleSheet(style)
        
        # 设置字体
        font = QFont('Arial', size_config['font_size'], QFont.Weight.Bold)
        self.setFont(font)
    
    def _update_layout(self, screen_size):
        """更新布局"""
        self._update_style()
    
    def _get_size_config(self) -> dict:
        """获取尺寸配置"""
        base_height = responsive_layout.get_button_height()
        base_font = responsive_layout.get_font_size("normal")
        
        size_configs = {
            ButtonSize.SMALL: {
                'min_height': int(base_height * 0.8),
                'min_width': 60,
                'padding_v': 4,
                'padding_h': 12,
                'font_size': int(base_font * 0.9),
                'border_radius': 4
            },
            ButtonSize.NORMAL: {
                'min_height': base_height,
                'min_width': 80,
                'padding_v': 8,
                'padding_h': 16,
                'font_size': base_font,
                'border_radius': 6
            },
            ButtonSize.LARGE: {
                'min_height': int(base_height * 1.2),
                'min_width': 100,
                'padding_v': 12,
                'padding_h': 20,
                'font_size': int(base_font * 1.1),
                'border_radius': 8
            }
        }
        
        return size_configs[self.button_size]
    
    def set_button_type(self, button_type: ButtonType):
        """设置按钮类型"""
        self.button_type = button_type
        self._update_style()
    
    def set_button_size(self, button_size: ButtonSize):
        """设置按钮尺寸"""
        self.button_size = button_size
        self._update_style()
    
    def set_loading(self, loading: bool):
        """设置加载状态"""
        self._is_loading = loading
        if loading:
            self.setText(tr("loading", "加载中..."))
            self.setEnabled(False)
        else:
            self.setText(self._original_text)
            self.setEnabled(True)
    
    def is_loading(self) -> bool:
        """获取加载状态"""
        return self._is_loading
    
    def set_text(self, text: str):
        """设置文本"""
        self._original_text = text
        if not self._is_loading:
            self.setText(text)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        super().enterEvent(event)
        # 可以添加悬停动画效果
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        super().leaveEvent(event)
        # 可以添加离开动画效果


class IconButton(ModernButton):
    """图标按钮"""
    
    def __init__(self, icon: QIcon, text: str = "", button_type: ButtonType = ButtonType.PRIMARY,
                 button_size: ButtonSize = ButtonSize.NORMAL, parent=None):
        super().__init__(text, button_type, button_size, icon, parent)
        
        # 设置图标尺寸
        icon_size = self._get_icon_size()
        self.setIconSize(icon_size)
    
    def _get_icon_size(self):
        """获取图标尺寸"""
        from PySide6.QtCore import QSize
        
        size_map = {
            ButtonSize.SMALL: QSize(16, 16),
            ButtonSize.NORMAL: QSize(20, 20),
            ButtonSize.LARGE: QSize(24, 24)
        }
        
        return size_map[self.button_size]
    
    def _update_style(self):
        """更新样式"""
        super()._update_style()
        icon_size = self._get_icon_size()
        self.setIconSize(icon_size)


class ToggleButton(ModernButton):
    """切换按钮"""
    
    # 状态切换信号
    toggled = Signal(bool)
    
    def __init__(self, text: str = "", checked: bool = False, 
                 button_type: ButtonType = ButtonType.PRIMARY,
                 button_size: ButtonSize = ButtonSize.NORMAL, parent=None):
        super().__init__(text, button_type, button_size, None, parent)
        
        self._checked = checked
        self.setCheckable(True)
        self.setChecked(checked)
        
        # 连接信号
        self.clicked.connect(self._on_clicked)
        
        # 更新样式
        self._update_toggle_style()
    
    def _on_clicked(self):
        """点击事件"""
        self._checked = self.isChecked()
        self._update_toggle_style()
        self.toggled.emit(self._checked)
    
    def _update_toggle_style(self):
        """更新切换样式"""
        if self._checked:
            # 选中状态使用不同的颜色
            self.set_button_type(ButtonType.SUCCESS)
        else:
            # 未选中状态使用默认颜色
            self.set_button_type(ButtonType.SECONDARY)
    
    def set_checked(self, checked: bool):
        """设置选中状态"""
        self._checked = checked
        super().setChecked(checked)
        self._update_toggle_style()
    
    def is_checked(self) -> bool:
        """获取选中状态"""
        return self._checked
