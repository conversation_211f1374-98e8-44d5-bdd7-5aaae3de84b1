# 网格曲线图类

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                               QHBoxLayout, QWidget, QGridLayout, QLabel)
from PySide6.QtCore import QTimer, Qt
from PySide6.QtGui import QFont
import sys
import pyqtgraph as pg

# 容器 + 布局 + 控件

class GridChartWindow(QMainWindow):
    # 网格曲线图类的 QMainWindow 是当这是主窗口时使用的，就像是函数中的mian
    def __init__(self):
        """__ 双下划线表示私有方法，属于魔法方法"""
        super().__init__()
        self.setWindowTitle("网格曲线图")
        self.setGeometry(100, 100, 1600, 1000)
        
        # 设置主窗口
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
            
        # 创建网格布局
        self.layout = QGridLayout(self.central_widget)
        
        # 初始化数据
        self.max_points = 100
        self.init_data() 
        
        # 创建图表
        self.create_charts()
        
        # 设置定时器 每50ms调用一次函数

        self.timer = QTimer()
        self.timer.timeout.connect(self.update_data)
        self.timer.start(50)

    def init_data(self):
        self.x_data = [1,2,3,4,5]
        self.y_data = [2,3,4,5,6]
    def create_charts(self):
        """创建图表"""
        # 创建布局
        containter = QWidget()
        layout = QVBoxLayout(containter)

        # 添加标题
        title_lable  = QLabel("网格曲线图")
        title_lable.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_lable.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title_lable)

        # 创建PyQtGraph图表
        self.chart = pg.PlotWidget()
        self.chart.setBackground('w')  # 白色背景
        self.chart.showGrid(x=True, y=True, alpha=0.3) # 显示网格
        self.chart.setLabel('left', 'Amplitude') # 设置y轴标签
        self.chart.setLabel('bottom', 'Time (s)') # 设置x轴标签

        # 设置性能优化
        self.chart.setClipToView(True) # 设置裁剪到视图
        self.chart.setDownsampling(mode='peak') # 设置下采样模式为峰值
        # self.chart.setRange(xRange=[0, 10], yRange=[-5, 5]) # 设置x轴和y轴的范围
        
        self.plot_cure = self.chart.plot(self.x_data,self.y_data,pen='b')
        # plot 的参数为 x轴的值，y轴的值

        layout.addWidget(self.chart)

        # 添加到主布局
        self.layout.addWidget(containter, 0, 0)

    def update_data(self):
        if not self.x_data:
            new_x = self.x_data[-1] + 1
            self.x_data.append(new_x)
            self.chart.plot(self.x_data, self.y_data, pen='b')
        else:
            new_x = self.x_data[-1]+1
            new_y = self.y_data[-1]
            self.x_data.append(new_x)
            self.y_data.append(new_y)
            self.plot_cure.setData(self.x_data, self.y_data)
        
        if len(self.x_data) >= self.max_points:
            self.x_data = self.x_data[-self.max_points:]
            self.y_data = self.y_data[-self.max_points:]
        
        self.chart.setRange(xRange=[min(self.x_data[0]+1), max(self.x_data)], yRange=[min(self.y_data), max(self.y_data)])


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = GridChartWindow()
    window.show()
    sys.exit(app.exec())

# 网格曲线图类

# from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
#                                QHBoxLayout, QWidget, QGridLayout, QLabel)
# from PySide6.QtCore import QTimer, Qt
# from PySide6.QtGui import QFont
# import sys
# import pyqtgraph as pg
# import numpy as np

# class GridChartWindow(QMainWindow):
#     def __init__(self):
#         super().__init__()
#         self.setWindowTitle("网格曲线图")
#         self.setGeometry(100, 100, 1600, 1000)
        
#         # 设置中心部件
#         self.central_widget = QWidget()
#         self.setCentralWidget(self.central_widget)
        
#         # 主布局
#         self.layout = QGridLayout(self.central_widget)
        
#         # 初始化数据和图表
#         self.max_points = 100
#         self.init_data()
#         self.create_charts()
        
#         # 定时器设置
#         self.timer = QTimer()
#         self.timer.timeout.connect(self.update_data)
#         self.timer.start(50)  # 50ms刷新
    
#     def init_data(self):
#         """初始化数据存储"""
#         self.x_data = []
#         self.y_data = []
    
#     def create_charts(self):
#         """创建图表布局和组件"""
#         container = QWidget()
#         layout = QVBoxLayout(container)
        
#         # 标题
#         title_label = QLabel("网格曲线图")
#         title_label.setAlignment(Qt.AlignCenter)  # 居中标题
#         title_label.setFont(QFont("Arial", 12, QFont.Bold))
#         layout.addWidget(title_label)
        
#         # 绘图部件
#         self.chart = pg.PlotWidget() # 画笔要在容器中使用
#         self.chart.setBackground('w')  # 白色背景
#         self.chart.showGrid(x=True, y=True, alpha=0.3)  # 显示网格
#         self.chart.setLabel('left', '振幅')  # Y轴标签
#         self.chart.setLabel('bottom', '时间 (秒)')  # X轴标签
#         # self.chart.setRange(xRange=[0, 10], yRange=[-5, 5])     # setRange设置坐标轴范围
        
#         # 性能优化设置
#         self.chart.setClipToView(True)
#         self.chart.setDownsampling(mode='peak')
        
#         # 初始化曲线
#         self.plot_curve = self.chart.plot(pen='b')  # 蓝色曲线
        
#         layout.addWidget(self.chart)
#         self.layout.addWidget(container, 0, 0)
    
#     def update_data(self):
#         """定时更新数据"""
#         if not self.x_data:
#             new_x = 0
#         else:
#             new_x = self.x_data[-1] + 0.1  # 时间递增
        
#         new_y = 5 * np.sin(new_x)  # 生成正弦波数据
#         self.x_data.append(new_x)
#         self.y_data.append(new_y)
        
#         # 限制数据长度
#         if len(self.x_data) > self.max_points:
#             self.x_data = self.x_data[-self.max_points:]
#             self.y_data = self.y_data[-self.max_points:]
        
#         # 更新曲线
#         self.plot_curve.setData(self.x_data, self.y_data)
#         self.chart.setRange(xRange=[min(self.x_data), max(self.x_data)], yRange=[min(self.y_data), max(self.y_data)])
#         # setRange设置坐标轴范围
#         self.chart.setXRange(min(self.x_data), max(self.x_data))
#         self.chart.setYRange(min(self.y_data), max(self.y_data))

# if __name__ == "__main__":  # 确保主程序入口正确
#     app = QApplication(sys.argv)
#     window = GridChartWindow()
#     window.show()
#     sys.exit(app.exec())
