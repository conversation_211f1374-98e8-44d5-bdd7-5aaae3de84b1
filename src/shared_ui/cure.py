import matplotlib.pyplot as plt
import numpy as np
# np 的主要对象是多维数组

# 模拟数据（时间序列）
time = np.arange(0, 10, 0.1) # 
data = np.sin(time) * 10  # 假设正常范围 [-8, 8]
threshold_high = 8 # 设置最高值
threshold_low = -8 # 设置最低值

# 拆分正常值和异常值
normal_mask = (data >= threshold_low) & (data <= threshold_high) 
abnormal_mask = ~normal_mask  # 表示对布尔数组按位取反  "~"按位取反操作符

# 绘制曲线
plt.figure(figsize=(12, 6))
plt.plot(time[normal_mask], data[normal_mask], 'b-', label='正常值')
plt.plot(time[abnormal_mask], data[abnormal_mask], 'r*', markersize=10, label='异常值')  # 红色星号标记异常

# 绘制阈值线
plt.axhline(threshold_high, color='orange', linestyle='--', label='上限')
plt.axhline(threshold_low, color='orange', linestyle='--', label='下限')

plt.legend()
plt.title("实时数据监测（异常值高亮）")
plt.show()
