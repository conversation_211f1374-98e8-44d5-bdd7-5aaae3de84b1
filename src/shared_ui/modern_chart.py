# -*- coding: utf-8 -*-
"""
现代化图表组件
支持多条曲线、实时更新、主题切换和响应式布局
"""

# 标准库
from typing import Dict, List, Tuple, Optional
from collections import deque
import time

# 第三方库
import pyqtgraph as pg
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont

# 本地库
from common.style.theme_manager import theme_manager
from common.style.responsive_layout import responsive_layout
from common.i18n.i18n_manager import tr


class ModernChart(QWidget):
    """现代化图表组件"""
    
    # 数据点击信号
    data_clicked = Signal(float, float)  # x, y
    
    def __init__(self, title: str = "", x_label: str = "", y_label: str = "", parent=None):
        super().__init__(parent)
        self.title = title
        self.x_label = x_label
        self.y_label = y_label
        
        # 数据存储
        self.curves = {}  # 存储曲线数据
        self.max_points = 1000  # 最大数据点数
        
        # 初始化UI
        self._init_ui()
        self._setup_chart()
        
        # 连接主题变化信号
        theme_manager.theme_changed.connect(self._update_theme)
        responsive_layout.screen_size_changed.connect(self._update_layout)
        
        # 应用初始主题
        self._update_theme()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(responsive_layout.get_spacing())
        
        # 标题栏
        if self.title:
            self.title_frame = QFrame()
            self.title_frame.setProperty("cardStyle", True)
            title_layout = QHBoxLayout(self.title_frame)
            
            self.title_label = QLabel(self.title)
            self.title_label.setProperty("labelType", "title")
            title_layout.addWidget(self.title_label)
            
            title_layout.addStretch()
            
            # 控制按钮
            self.reset_btn = QPushButton(tr("reset", "重置"))
            self.reset_btn.clicked.connect(self.clear_all_data)
            title_layout.addWidget(self.reset_btn)
            
            layout.addWidget(self.title_frame)
        
        # 图表区域
        self.chart_widget = pg.PlotWidget()
        self.chart_widget.setProperty("chartStyle", True)
        layout.addWidget(self.chart_widget)
        
        # 设置最小高度
        chart_height = responsive_layout.get_chart_height()
        self.chart_widget.setMinimumHeight(chart_height)
    
    def _setup_chart(self):
        """设置图表"""
        # 设置标签
        if self.x_label:
            self.chart_widget.setLabel('bottom', self.x_label)
        if self.y_label:
            self.chart_widget.setLabel('left', self.y_label)
        
        # 启用网格
        self.chart_widget.showGrid(x=True, y=True, alpha=0.3)
        
        # 设置性能优化
        self.chart_widget.setClipToView(True)
        self.chart_widget.setDownsampling(mode='peak')
        
        # 启用鼠标交互
        self.chart_widget.setMouseEnabled(x=True, y=True)
        
        # 连接点击事件
        self.chart_widget.scene().sigMouseClicked.connect(self._on_mouse_clicked)
    
    def _update_theme(self):
        """更新主题"""
        colors = theme_manager.get_colors()
        
        # 设置背景色
        self.chart_widget.setBackground(colors['CARD_BG'])
        
        # 设置网格颜色
        self.chart_widget.getPlotItem().getAxis('bottom').setPen(colors['CHART_AXIS'])
        self.chart_widget.getPlotItem().getAxis('left').setPen(colors['CHART_AXIS'])
        self.chart_widget.getPlotItem().getAxis('bottom').setTextPen(colors['TEXT'])
        self.chart_widget.getPlotItem().getAxis('left').setTextPen(colors['TEXT'])
        
        # 更新网格颜色
        self.chart_widget.showGrid(x=True, y=True, alpha=0.3)
        
        # 更新字体
        font_size = responsive_layout.get_font_size("normal")
        font = QFont('Arial', font_size)
        self.chart_widget.getPlotItem().getAxis('bottom').setStyle(tickFont=font)
        self.chart_widget.getPlotItem().getAxis('left').setStyle(tickFont=font)
    
    def _update_layout(self, screen_size):
        """更新布局"""
        chart_height = responsive_layout.get_chart_height()
        self.chart_widget.setMinimumHeight(chart_height)
        
        spacing = responsive_layout.get_spacing()
        self.layout().setSpacing(spacing)
    
    def _on_mouse_clicked(self, event):
        """鼠标点击事件"""
        if event.double():
            # 双击重置视图
            self.chart_widget.autoRange()
        else:
            # 单击获取坐标
            pos = event.scenePos()
            if self.chart_widget.sceneBoundingRect().contains(pos):
                mouse_point = self.chart_widget.getPlotItem().vb.mapSceneToView(pos)
                self.data_clicked.emit(mouse_point.x(), mouse_point.y())
    
    def add_curve(self, name: str, color: str = None, width: int = 2, style: str = 'solid') -> bool:
        """添加曲线"""
        if name in self.curves:
            return False
        
        # 自动分配颜色
        if color is None:
            colors = theme_manager.get_colors()
            color_keys = ['CHART_LINE_1', 'CHART_LINE_2', 'CHART_LINE_3', 'CHART_LINE_4']
            color = colors[color_keys[len(self.curves) % len(color_keys)]]
        
        # 创建曲线
        pen_style = Qt.PenStyle.SolidLine if style == 'solid' else Qt.PenStyle.DashLine
        pen = pg.mkPen(color=color, width=width, style=pen_style)
        
        curve = self.chart_widget.plot([], [], pen=pen, name=name)
        
        # 存储曲线信息
        self.curves[name] = {
            'curve': curve,
            'x_data': deque(maxlen=self.max_points),
            'y_data': deque(maxlen=self.max_points),
            'color': color,
            'width': width,
            'style': style
        }
        
        return True
    
    def remove_curve(self, name: str) -> bool:
        """移除曲线"""
        if name not in self.curves:
            return False
        
        # 从图表中移除
        self.chart_widget.removeItem(self.curves[name]['curve'])
        
        # 从存储中删除
        del self.curves[name]
        
        return True
    
    def update_curve_data(self, name: str, x: float, y: float) -> bool:
        """更新曲线数据"""
        if name not in self.curves:
            return False
        
        curve_data = self.curves[name]
        curve_data['x_data'].append(x)
        curve_data['y_data'].append(y)
        
        # 更新图表显示
        curve_data['curve'].setData(list(curve_data['x_data']), list(curve_data['y_data']))
        
        return True
    
    def set_curve_data(self, name: str, x_data: List[float], y_data: List[float]) -> bool:
        """设置曲线完整数据"""
        if name not in self.curves:
            return False
        
        if len(x_data) != len(y_data):
            return False
        
        curve_data = self.curves[name]
        curve_data['x_data'].clear()
        curve_data['y_data'].clear()
        
        # 限制数据点数量
        if len(x_data) > self.max_points:
            x_data = x_data[-self.max_points:]
            y_data = y_data[-self.max_points:]
        
        curve_data['x_data'].extend(x_data)
        curve_data['y_data'].extend(y_data)
        
        # 更新图表显示
        curve_data['curve'].setData(x_data, y_data)
        
        return True
    
    def clear_curve_data(self, name: str) -> bool:
        """清空曲线数据"""
        if name not in self.curves:
            return False
        
        curve_data = self.curves[name]
        curve_data['x_data'].clear()
        curve_data['y_data'].clear()
        curve_data['curve'].setData([], [])
        
        return True
    
    def clear_all_data(self):
        """清空所有数据"""
        for name in self.curves:
            self.clear_curve_data(name)
    
    def set_x_range(self, min_val: float, max_val: float):
        """设置X轴范围"""
        self.chart_widget.setXRange(min_val, max_val)
    
    def set_y_range(self, min_val: float, max_val: float):
        """设置Y轴范围"""
        self.chart_widget.setYRange(min_val, max_val)
    
    def auto_range(self):
        """自动调整范围"""
        self.chart_widget.autoRange()
    
    def get_curve_names(self) -> List[str]:
        """获取所有曲线名称"""
        return list(self.curves.keys())
    
    def set_max_points(self, max_points: int):
        """设置最大数据点数"""
        self.max_points = max_points
        for curve_data in self.curves.values():
            curve_data['x_data'] = deque(curve_data['x_data'], maxlen=max_points)
            curve_data['y_data'] = deque(curve_data['y_data'], maxlen=max_points)
