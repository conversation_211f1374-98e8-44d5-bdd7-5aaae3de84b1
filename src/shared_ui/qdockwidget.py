from PyQt5.QtWidgets import QApplication, QMainWindow, QDockWidget, QTextEdit, QPushButton

app = QApplication([])

# 创建主窗口
main_window = QMainWindow()
main_window.setWindowTitle("可停靠工具栏示例")

# 创建文本编辑器
text_edit = QTextEdit()
main_window.setCentralWidget(text_edit)

# 创建QDockWidget作为可停靠的工具栏
dock_widget = QDockWidget("工具栏", main_window)
button = QPushButton("点击我")
dock_widget.setWidget(button)

# 将工具栏停靠在主窗口的右侧
main_window.addDockWidget(2, dock_widget)

main_window.show()
app.exec_()

