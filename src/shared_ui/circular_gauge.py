# -*- coding: utf-8 -*-
"""
圆形仪表盘组件
支持主题切换和响应式布局
"""

# 标准库
import math

# 第三方库
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Qt, QRectF, QPropertyAnimation, QEasingCurve, Property
from PySide6.QtGui import QPainter, QPen, QColor, QFont, QRadialGradient, QLinearGradient, QPainterPath

# 本地库
from common.style.theme_manager import theme_manager
from common.style.responsive_layout import responsive_layout
from common.i18n.i18n_manager import tr


class CircularGauge(QWidget):
    def __init__(self, title, min_val, max_val, unit='', parent=None):
        super().__init__(parent)
        self.title = title
        self.min_val = min_val
        self.max_val = max_val
        self.unit = unit
        self.current_value = min_val  # 当前显示值
        self.target_value = min_val   # 目标值

        # 响应式尺寸设置
        gauge_size = responsive_layout.get_gauge_size()
        self.setMinimumSize(gauge_size, gauge_size)
        self.setMaximumSize(gauge_size, gauge_size)

        # 创建动画对象
        self.animation = QPropertyAnimation(self, b"value")
        self.animation.setDuration(500)  # 设置动画持续时间为500毫秒
        self.animation.setEasingCurve(QEasingCurve.OutCubic)  # 设置缓动曲线

        # 连接主题变化信号
        theme_manager.theme_changed.connect(self.update)
        responsive_layout.screen_size_changed.connect(self._on_screen_size_changed)

    def _on_screen_size_changed(self, screen_size):
        """屏幕尺寸变化处理"""
        gauge_size = responsive_layout.get_gauge_size()
        self.setMinimumSize(gauge_size, gauge_size)
        self.setMaximumSize(gauge_size, gauge_size)
        self.update()

    @Property(float)
    def value(self):
        return self.current_value
        
    @value.setter
    def value(self, val):
        self.current_value = val
        self.update()
        
    def setValue(self, value):
        # 确保值在有效范围内
        value = max(self.min_val, min(self.max_val, value))
        
        # 如果动画正在运行，先停止它
        if self.animation.state() == QPropertyAnimation.State.Running:
            self.animation.stop()
            
        # 设置动画的起始值和结束值
        self.animation.setStartValue(self.current_value)
        self.animation.setEndValue(value)
        
        # 启动动画
        self.animation.start()
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 计算中心点和半径
        width = self.width()
        height = self.height()
        center_x = width / 2
        center_y = height / 2
        radius = min(width, height) / 2 - 35
        
        # 获取当前主题颜色
        colors = theme_manager.get_colors()

        # 绘制背景
        gradient = QRadialGradient(center_x, center_y, radius)
        gradient.setColorAt(0.0, QColor(colors["GAUGE_BG"]))
        gradient.setColorAt(1.0, QColor(colors["MAIN_BG"]))
        painter.setBrush(gradient)
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(int(center_x - radius), int(center_y - radius),
                          int(radius * 2), int(radius * 2))

        # 绘制标题
        painter.setPen(QColor(colors["GAUGE_TEXT"]))
        title_font_size = responsive_layout.get_font_size("normal")
        painter.setFont(QFont('Arial', title_font_size, QFont.Weight.Bold))
        painter.drawText(0, 10, width, 30, Qt.AlignmentFlag.AlignCenter, self.title)

        # 绘制外圈
        pen = QPen(QColor(colors["GAUGE_BORDER"]), 3)
        pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(pen)
        painter.drawArc(int(center_x - radius), int(center_y - radius),
                       int(radius * 2), int(radius * 2), 225 * 16, -270 * 16)
        
        # 绘制刻度线和刻度值
        num_ticks = 10
        for i in range(num_ticks + 1):
            angle = -225 + i * 270 / num_ticks
            rad = math.radians(angle)

            # 主刻度线
            pen = QPen(QColor(colors["GAUGE_BORDER"]), 2 if i % 2 == 0 else 1)
            pen.setCapStyle(Qt.PenCapStyle.RoundCap)
            painter.setPen(pen)

            inner_x = center_x + (radius - 15) * math.cos(rad)
            inner_y = center_y + (radius - 15) * math.sin(rad)
            outer_x = center_x + radius * math.cos(rad)
            outer_y = center_y + radius * math.sin(rad)
            painter.drawLine(int(inner_x), int(inner_y), int(outer_x), int(outer_y))

            # 绘制刻度值
            if i % 2 == 0:
                tick_value = self.min_val + i * (self.max_val - self.min_val) / num_ticks
                text = f"{tick_value:.1f}"

                text_radius = radius - 35
                text_x = center_x + text_radius * math.cos(rad)
                text_y = center_y + text_radius * math.sin(rad)

                flags = Qt.AlignmentFlag.AlignCenter
                text_rect = QRectF(text_x - 20, text_y - 10, 40, 20)

                tick_font_size = responsive_layout.get_font_size("small")
                painter.setFont(QFont('Arial', tick_font_size))
                painter.setPen(QColor(colors["GAUGE_TEXT"]))
                painter.drawText(text_rect, flags, text)
        
        # 绘制指针
        value_angle = -225 + (self.value - self.min_val) * 270 / (self.max_val - self.min_val)
        value_rad = math.radians(value_angle)

        # 绘制指针阴影
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(QColor(0, 0, 0, 50))  # 增加阴影透明度
        
        # 创建指针形状
        pointer_length = radius - 20
        pointer_width = 8  # 指针宽度
        
        # 计算指针顶点
        tip_x = center_x + pointer_length * math.cos(value_rad)
        tip_y = center_y + pointer_length * math.sin(value_rad)
        
        # 计算指针底部两个点
        base_angle1 = value_rad + math.pi/2
        base_angle2 = value_rad - math.pi/2
        base_x1 = center_x + pointer_width * math.cos(base_angle1)
        base_y1 = center_y + pointer_width * math.sin(base_angle1)
        base_x2 = center_x + pointer_width * math.cos(base_angle2)
        base_y2 = center_y + pointer_width * math.sin(base_angle2)
        
        # 绘制指针阴影
        shadow_path = QPainterPath()
        shadow_path.moveTo(base_x1 + 2, base_y1 + 2)
        shadow_path.lineTo(tip_x + 2, tip_y + 2)
        shadow_path.lineTo(base_x2 + 2, base_y2 + 2)
        shadow_path.lineTo(base_x1 + 2, base_y1 + 2)
        painter.drawPath(shadow_path)
        
        # 绘制指针主体
        gradient = QLinearGradient(center_x, center_y, tip_x, tip_y)
        gradient.setColorAt(0.0, QColor(colors['GAUGE_NEEDLE']))
        gradient.setColorAt(1.0, QColor(colors['ERROR']))
        painter.setBrush(gradient)

        pointer_path = QPainterPath()
        pointer_path.moveTo(base_x1, base_y1)
        pointer_path.lineTo(tip_x, tip_y)
        pointer_path.lineTo(base_x2, base_y2)
        pointer_path.lineTo(base_x1, base_y1)
        painter.drawPath(pointer_path)

        # 绘制中心圆
        # 外圈
        outer_gradient = QRadialGradient(center_x, center_y, 10)
        outer_gradient.setColorAt(0.0, QColor(colors['TEXT_SECONDARY']))
        outer_gradient.setColorAt(1.0, QColor(colors['BORDER']))
        painter.setBrush(outer_gradient)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(int(center_x - 10), int(center_y - 10), 20, 20)

        # 内圈
        inner_gradient = QRadialGradient(center_x, center_y, 6)
        inner_gradient.setColorAt(0.0, QColor(colors['GAUGE_NEEDLE']))
        inner_gradient.setColorAt(1.0, QColor(colors['ERROR']))
        painter.setBrush(inner_gradient)
        painter.drawEllipse(int(center_x - 6), int(center_y - 6), 12, 12)

        # 绘制当前值
        painter.setPen(QColor(colors["GAUGE_TEXT"]))
        value_font_size = responsive_layout.get_font_size("normal")
        painter.setFont(QFont('Arial', value_font_size, QFont.Weight.Bold))
        current_value = f"{self.value:.1f}{self.unit}"
        value_rect = QRectF(0, center_y + radius/2, width, 30)
        painter.drawText(value_rect, Qt.AlignmentFlag.AlignCenter, current_value)
