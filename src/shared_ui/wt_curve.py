import sys
import pandas as pd
import threading
import time
from datetime import datetime
from collections import deque
import numpy as np

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                               QHBoxLayout, QWidget, QPushButton, QLabel,
                               QFileDialog, QMessageBox, QComboBox, QCheckBox,
                               QGridLayout, QGroupBox, QTabWidget, QSplitter)
from PySide6.QtCore import QTimer, Signal, QObject, Qt
from PySide6.QtGui import QFont

import pyqtgraph as pg


class DataReader(QObject):
    """数据读取器，负责从CSV文件读取数据并按指定速率发送"""
    data_ready = Signal(dict)
    
    def __init__(self):
        super().__init__()
        self.data = None
        self.current_index = 0
        self.is_running = False
        self.timer = QTimer()
        self.timer.timeout.connect(self.emit_data)
        
    def load_file(self, file_path):
        """加载CSV文件"""
        try:
            # 读取CSV文件
            self.data = pd.read_csv(file_path, encoding='utf-8')
            
            # 打印列名用于调试
            print(f"CSV列名: {list(self.data.columns)}")
            
            # 检查必要的列是否存在
            required_columns = ['时间', 'Depth', 'Speed', 'Tension']
            sensor_columns = []
            for wt in range(1, 7):
                for axis in ['X', 'Y', 'Z']:
                    col_name = f'WT{wt}_{axis}'
                    required_columns.append(col_name)
                    sensor_columns.append(col_name)
            
            missing_columns = [col for col in required_columns if col not in self.data.columns]
            if missing_columns:
                print(f"缺少列: {missing_columns}")
                return False
            
            # 确保数据类型正确
            numeric_columns = ['Depth', 'Speed', 'Tension'] + sensor_columns
            for col in numeric_columns:
                self.data[col] = pd.to_numeric(self.data[col], errors='coerce')
            
            # 移除包含NaN的行
            self.data = self.data.dropna(subset=numeric_columns)
            
            self.current_index = 0
            print(f"成功加载 {len(self.data)} 行数据")
            return True
            
        except Exception as e:
            print(f"加载文件失败: {e}")
            return False
    
    def start_reading(self):
        """开始读取数据，1秒发送20个点"""
        if self.data is None or len(self.data) == 0:
            return False
        
        self.is_running = True
        # 1秒20个点，即每50ms发送一个点
        self.timer.start(50)
        return True
    
    def stop_reading(self):
        """停止读取数据"""
        self.is_running = False
        self.timer.stop()
    
    def reset_data(self):
        """重置数据索引"""
        self.current_index = 0
    
    def emit_data(self):
        """发送数据"""
        if not self.is_running or self.current_index >= len(self.data):
            # 数据播放完毕，重新开始
            self.current_index = 0
            if not self.is_running:
                return
        
        row = self.data.iloc[self.current_index]
        
        # 解析数据
        try:
            data = {
                'timestamp': str(row['时间']),
                'depth': float(row['Depth']),
                'speed': float(row['Speed']),
                'tension': float(row['Tension']),
                'WT1': {
                    'X': float(row['WT1_X']),
                    'Y': float(row['WT1_Y']),
                    'Z': float(row['WT1_Z'])
                },
                'WT2': {
                    'X': float(row['WT2_X']),
                    'Y': float(row['WT2_Y']),
                    'Z': float(row['WT2_Z'])
                },
                'WT3': {
                    'X': float(row['WT3_X']),
                    'Y': float(row['WT3_Y']),
                    'Z': float(row['WT3_Z'])
                },
                'WT4': {
                    'X': float(row['WT4_X']),
                    'Y': float(row['WT4_Y']),
                    'Z': float(row['WT4_Z'])
                },
                'WT5': {
                    'X': float(row['WT5_X']),
                    'Y': float(row['WT5_Y']),
                    'Z': float(row['WT5_Z'])
                },
                'WT6': {
                    'X': float(row['WT6_X']),
                    'Y': float(row['WT6_Y']),
                    'Z': float(row['WT6_Z'])
                }
            }
            self.data_ready.emit(data)
            self.current_index += 1
            
        except (ValueError, KeyError) as e:
            print(f"数据解析错误 (行 {self.current_index}): {e}")
            self.current_index += 1


class SensorPlotWidget(QWidget):
    """单个传感器的绘图控件"""
    
    def __init__(self, sensor_name, max_points=1000):
        super().__init__()
        self.sensor_name = sensor_name
        self.max_points = max_points
        
        # 数据缓存
        self.time_data = deque(maxlen=max_points)
        self.x_data = deque(maxlen=max_points)
        self.y_data = deque(maxlen=max_points)
        self.z_data = deque(maxlen=max_points)
        
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # 传感器标题和控制
        header_layout = QHBoxLayout()
        
        title_label = QLabel(f"传感器 {self.sensor_name}")
        title_label.setFont(QFont("Arial", 10, QFont.Bold))
        header_layout.addWidget(title_label)
        
        # 轴显示控制
        self.x_checkbox = QCheckBox("X轴")
        self.x_checkbox.setChecked(True)
        self.x_checkbox.stateChanged.connect(self.update_visibility)
        
        self.y_checkbox = QCheckBox("Y轴")
        self.y_checkbox.setChecked(True)
        self.y_checkbox.stateChanged.connect(self.update_visibility)
        
        self.z_checkbox = QCheckBox("Z轴")
        self.z_checkbox.setChecked(True)
        self.z_checkbox.stateChanged.connect(self.update_visibility)
        
        header_layout.addWidget(self.x_checkbox)
        header_layout.addWidget(self.y_checkbox)
        header_layout.addWidget(self.z_checkbox)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # 创建绘图控件
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setLabel('left', 'Value')
        self.plot_widget.setLabel('bottom', 'Time Points')
        self.plot_widget.showGrid(x=True, y=True)
        self.plot_widget.setBackground('w')
        
        # 创建曲线
        self.x_curve = self.plot_widget.plot(pen=pg.mkPen('r', width=2), name='X轴')
        self.y_curve = self.plot_widget.plot(pen=pg.mkPen('g', width=2), name='Y轴')
        self.z_curve = self.plot_widget.plot(pen=pg.mkPen('b', width=2), name='Z轴')
        
        # 添加图例
        self.plot_widget.addLegend()
        
        layout.addWidget(self.plot_widget)
        self.setLayout(layout)
    
    def update_data(self, data):
        """更新数据"""
        current_time = len(self.time_data)
        
        self.time_data.append(current_time)
        self.x_data.append(data['X'])
        self.y_data.append(data['Y'])
        self.z_data.append(data['Z'])
        
        # 更新曲线
        self.x_curve.setData(list(self.time_data), list(self.x_data))
        self.y_curve.setData(list(self.time_data), list(self.y_data))
        self.z_curve.setData(list(self.time_data), list(self.z_data))
    
    def update_visibility(self):
        """更新曲线可见性"""
        self.x_curve.setVisible(self.x_checkbox.isChecked())
        self.y_curve.setVisible(self.y_checkbox.isChecked())
        self.z_curve.setVisible(self.z_checkbox.isChecked())
    
    def clear_data(self):
        """清除数据"""
        self.time_data.clear()
        self.x_data.clear()
        self.y_data.clear()
        self.z_data.clear()
        
        self.x_curve.setData([], [])
        self.y_curve.setData([], [])
        self.z_curve.setData([], [])


class ProcessParameterWidget(QWidget):
    """工艺参数显示控件"""
    
    def __init__(self, max_points=1000):
        super().__init__()
        self.max_points = max_points
        
        # 数据缓存
        self.time_data = deque(maxlen=max_points)
        self.depth_data = deque(maxlen=max_points)
        self.speed_data = deque(maxlen=max_points)
        self.tension_data = deque(maxlen=max_points)
        
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # 标题和控制
        header_layout = QHBoxLayout()
        
        title_label = QLabel("工艺参数")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        header_layout.addWidget(title_label)
        
        # 参数显示控制
        self.depth_checkbox = QCheckBox("深度 (Depth)")
        self.depth_checkbox.setChecked(True)
        self.depth_checkbox.stateChanged.connect(self.update_visibility)
        
        self.speed_checkbox = QCheckBox("速度 (Speed)")
        self.speed_checkbox.setChecked(True)
        self.speed_checkbox.stateChanged.connect(self.update_visibility)
        
        self.tension_checkbox = QCheckBox("张力 (Tension)")
        self.tension_checkbox.setChecked(True)
        self.tension_checkbox.stateChanged.connect(self.update_visibility)
        
        header_layout.addWidget(self.depth_checkbox)
        header_layout.addWidget(self.speed_checkbox)
        header_layout.addWidget(self.tension_checkbox)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # 创建绘图控件
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setLabel('left', 'Value')
        self.plot_widget.setLabel('bottom', 'Time Points')
        self.plot_widget.showGrid(x=True, y=True)
        self.plot_widget.setBackground('w')
        
        # 创建曲线
        self.depth_curve = self.plot_widget.plot(pen=pg.mkPen('purple', width=2), name='深度')
        self.speed_curve = self.plot_widget.plot(pen=pg.mkPen('orange', width=2), name='速度')
        self.tension_curve = self.plot_widget.plot(pen=pg.mkPen('brown', width=2), name='张力')
        
        # 添加图例
        self.plot_widget.addLegend()
        
        layout.addWidget(self.plot_widget)
        self.setLayout(layout)
    
    def update_data(self, depth, speed, tension):
        """更新数据"""
        current_time = len(self.time_data)
        
        self.time_data.append(current_time)
        self.depth_data.append(depth)
        self.speed_data.append(speed)
        self.tension_data.append(tension)
        
        # 更新曲线
        self.depth_curve.setData(list(self.time_data), list(self.depth_data))
        self.speed_curve.setData(list(self.time_data), list(self.speed_data))
        self.tension_curve.setData(list(self.time_data), list(self.tension_data))
    
    def update_visibility(self):
        """更新曲线可见性"""
        self.depth_curve.setVisible(self.depth_checkbox.isChecked())
        self.speed_curve.setVisible(self.speed_checkbox.isChecked())
        self.tension_curve.setVisible(self.tension_checkbox.isChecked())
    
    def clear_data(self):
        """清除数据"""
        self.time_data.clear()
        self.depth_data.clear()
        self.speed_data.clear()
        self.tension_data.clear()
        
        self.depth_curve.setData([], [])
        self.speed_curve.setData([], [])
        self.tension_curve.setData([], [])


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.data_reader = DataReader()
        self.sensor_plots = {}
        self.process_parameter_widget = None
        
        self.setup_ui()
        self.connect_signals()
        
    def setup_ui(self):
        self.setWindowTitle("传感器实时数据监控系统")
        self.setGeometry(100, 100, 1800, 1200)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout()
        
        # 控制面板
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)
        
        # 数据显示区域
        data_display_area = self.create_data_display_area()
        main_layout.addWidget(data_display_area)
        
        central_widget.setLayout(main_layout)
    
    def create_control_panel(self):
        """创建控制面板"""
        group_box = QGroupBox("控制面板")
        layout = QHBoxLayout()
        
        # 文件选择
        self.file_label = QLabel("未选择文件")
        self.file_label.setMaximumWidth(300)
        self.file_button = QPushButton("选择CSV文件")
        self.file_button.clicked.connect(self.select_file)
        
        # 控制按钮
        self.start_button = QPushButton("开始监控")
        self.start_button.clicked.connect(self.start_monitoring)
        self.start_button.setEnabled(False)
        
        self.stop_button = QPushButton("停止监控")
        self.stop_button.clicked.connect(self.stop_monitoring)
        self.stop_button.setEnabled(False)
        
        self.reset_button = QPushButton("重置数据")
        self.reset_button.clicked.connect(self.reset_data)
        
        self.clear_button = QPushButton("清除图表")
        self.clear_button.clicked.connect(self.clear_data)
        
        # 当前值显示
        self.current_values_label = QLabel("当前值: 深度=0, 速度=0, 张力=0")
        self.current_values_label.setFont(QFont("Arial", 10))
        
        # 状态显示
        self.status_label = QLabel("状态: 就绪")
        
        layout.addWidget(QLabel("文件:"))
        layout.addWidget(self.file_label)
        layout.addWidget(self.file_button)
        layout.addStretch()
        layout.addWidget(self.start_button)
        layout.addWidget(self.stop_button)
        layout.addWidget(self.reset_button)
        layout.addWidget(self.clear_button)
        layout.addStretch()
        layout.addWidget(self.current_values_label)
        layout.addStretch()
        layout.addWidget(self.status_label)
        
        group_box.setLayout(layout)
        return group_box
    
    def create_data_display_area(self):
        """创建数据显示区域"""
        # 使用分割器来分割工艺参数和传感器数据
        splitter = QSplitter(Qt.Vertical)
        
        # 工艺参数显示
        self.process_parameter_widget = ProcessParameterWidget()
        splitter.addWidget(self.process_parameter_widget)
        
        # 传感器数据显示
        sensor_widget = QWidget()
        sensor_layout = QGridLayout()
        
        # 创建6个传感器的绘图控件，2行3列布局
        sensors = ['WT1', 'WT2', 'WT3', 'WT4', 'WT5', 'WT6']
        
        for i, sensor in enumerate(sensors):
            row = i // 3
            col = i % 3
            
            plot_widget = SensorPlotWidget(sensor)
            self.sensor_plots[sensor] = plot_widget
            sensor_layout.addWidget(plot_widget, row, col)
        
        sensor_widget.setLayout(sensor_layout)
        splitter.addWidget(sensor_widget)
        
        # 设置分割比例：工艺参数占1/4，传感器数据占3/4
        splitter.setSizes([300, 900])
        
        return splitter
    
    def connect_signals(self):
        """连接信号"""
        self.data_reader.data_ready.connect(self.update_displays)
    
    def select_file(self):
        """选择CSV文件 设置intialdir"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择CSV文件", r"/Users/<USER>/Desktop/shuju2.1.csv", 
            "CSV Files (*.csv);;All Files (*)"
        )

        
        
        if file_path:
            if self.data_reader.load_file(file_path):
                file_name = file_path.split('/')[-1]
                self.file_label.setText(f"已选择: {file_name}")
                self.start_button.setEnabled(True)
                self.status_label.setText("状态: 文件已加载")
            else:
                QMessageBox.warning(self, "错误", "无法加载CSV文件，请检查文件格式")
    
    def start_monitoring(self):
        """开始监控"""
        if self.data_reader.start_reading():
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.file_button.setEnabled(False)
            self.status_label.setText("状态: 监控中...")
        else:
            QMessageBox.warning(self, "错误", "无法开始监控，请检查数据文件")
    
    def stop_monitoring(self):
        """停止监控"""
        self.data_reader.stop_reading()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.file_button.setEnabled(True)
        self.status_label.setText("状态: 已停止")
    
    def reset_data(self):
        """重置数据"""
        self.data_reader.reset_data()
        self.clear_data()
        self.status_label.setText("状态: 数据已重置")
    
    def clear_data(self):
        """清除所有数据"""
        for plot_widget in self.sensor_plots.values():
            plot_widget.clear_data()
        if self.process_parameter_widget:
            self.process_parameter_widget.clear_data()
        self.current_values_label.setText("当前值: 深度=0, 速度=0, 张力=0")
        self.status_label.setText("状态: 图表已清除")
    
    def update_displays(self, data):
        """更新所有显示"""
        # 更新传感器绘图
        for sensor_name, sensor_data in data.items():
            if sensor_name.startswith('WT') and sensor_name in self.sensor_plots:
                self.sensor_plots[sensor_name].update_data(sensor_data)
        
        # 更新工艺参数绘图
        if self.process_parameter_widget:
            self.process_parameter_widget.update_data(
                data['depth'], data['speed'], data['tension']
            )
        
        # 更新当前值显示
        self.current_values_label.setText(
            f"当前值: 深度={data['depth']:.2f}, 速度={data['speed']:.1f}, 张力={data['tension']:.1f}, 时间={data['timestamp']}"
        )
    
    def closeEvent(self, event):
        """关闭事件"""
        self.data_reader.stop_reading()
        event.accept()


def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()