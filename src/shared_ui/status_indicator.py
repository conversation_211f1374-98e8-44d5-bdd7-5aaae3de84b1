# -*- coding: utf-8 -*-
"""
状态指示器组件
用于显示设备状态、报警信息等
"""

# 标准库
from typing import Optional
from enum import Enum

# 第三方库
from PySide6.QtWidgets import QWidget, QHBoxLayout, QVBoxLayout, QLabel, QFrame
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, Property
from PySide6.QtGui import QPainter, QColor, QFont, QPen, QBrush

# 本地库
from common.style.theme_manager import theme_manager
from common.style.responsive_layout import responsive_layout
from common.i18n.i18n_manager import tr


class StatusType(Enum):
    """状态类型枚举"""
    NORMAL = "normal"
    WARNING = "warning"
    ERROR = "error"
    INFO = "info"
    OFFLINE = "offline"


class StatusIndicator(QWidget):
    """状态指示器组件"""
    
    def __init__(self, title: str = "", status: StatusType = StatusType.NORMAL, 
                 message: str = "", show_animation: bool = True, parent=None):
        super().__init__(parent)
        
        self.title = title
        self.status = status
        self.message = message
        self.show_animation = show_animation
        self._opacity = 1.0
        
        # 初始化UI
        self._init_ui()
        
        # 设置动画
        if show_animation:
            self._setup_animation()
        
        # 连接主题变化信号
        theme_manager.theme_changed.connect(self._update_style)
        responsive_layout.screen_size_changed.connect(self._update_layout)
        
        # 应用初始样式
        self._update_style()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(responsive_layout.get_spacing())
        
        # 状态指示灯
        self.indicator_widget = StatusLight(self.status)
        layout.addWidget(self.indicator_widget)
        
        # 文本区域
        text_layout = QVBoxLayout()
        text_layout.setSpacing(2)
        
        # 标题
        if self.title:
            self.title_label = QLabel(self.title)
            self.title_label.setProperty("labelType", "normal")
            text_layout.addWidget(self.title_label)
        
        # 消息
        if self.message:
            self.message_label = QLabel(self.message)
            self.message_label.setProperty("labelType", "secondary")
            self.message_label.setWordWrap(True)
            text_layout.addWidget(self.message_label)
        
        layout.addLayout(text_layout)
        layout.addStretch()
        
        # 设置框架样式
        self.setProperty("cardStyle", True)
    
    def _setup_animation(self):
        """设置动画"""
        if self.status in [StatusType.WARNING, StatusType.ERROR]:
            # 闪烁动画
            self.blink_timer = QTimer()
            self.blink_timer.timeout.connect(self._blink)
            self.blink_timer.start(1000)  # 每秒闪烁一次
            
            # 透明度动画
            self.opacity_animation = QPropertyAnimation(self, b"opacity")
            self.opacity_animation.setDuration(500)
            self.opacity_animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
    
    @Property(float)
    def opacity(self):
        return self._opacity
    
    @opacity.setter
    def opacity(self, value):
        self._opacity = value
        self.update()
    
    def _blink(self):
        """闪烁效果"""
        if hasattr(self, 'opacity_animation'):
            if self.opacity_animation.state() == QPropertyAnimation.State.Running:
                return
            
            if self._opacity > 0.5:
                self.opacity_animation.setStartValue(1.0)
                self.opacity_animation.setEndValue(0.3)
            else:
                self.opacity_animation.setStartValue(0.3)
                self.opacity_animation.setEndValue(1.0)
            
            self.opacity_animation.start()
    
    def _update_style(self):
        """更新样式"""
        colors = theme_manager.get_colors()
        
        # 根据状态设置背景色
        status_colors = {
            StatusType.NORMAL: colors['SUCCESS'],
            StatusType.WARNING: colors['WARNING'],
            StatusType.ERROR: colors['ERROR'],
            StatusType.INFO: colors['INFO'],
            StatusType.OFFLINE: colors['TEXT_DISABLED']
        }
        
        status_color = status_colors[self.status]
        
        style = f"""
        QWidget[cardStyle="true"] {{
            background-color: {colors['CARD_BG']};
            border-left: 4px solid {status_color};
            border-radius: 6px;
            margin: 4px;
        }}
        """
        
        self.setStyleSheet(style)
        
        # 更新指示灯
        if hasattr(self, 'indicator_widget'):
            self.indicator_widget.set_status(self.status)
    
    def _update_layout(self, screen_size):
        """更新布局"""
        spacing = responsive_layout.get_spacing()
        self.layout().setSpacing(spacing)
    
    def set_status(self, status: StatusType, message: str = None):
        """设置状态"""
        self.status = status
        if message is not None:
            self.message = message
            if hasattr(self, 'message_label'):
                self.message_label.setText(message)
        
        # 更新指示灯
        if hasattr(self, 'indicator_widget'):
            self.indicator_widget.set_status(status)
        
        # 重新设置动画
        if self.show_animation:
            if hasattr(self, 'blink_timer'):
                self.blink_timer.stop()
            
            if status in [StatusType.WARNING, StatusType.ERROR]:
                if not hasattr(self, 'blink_timer'):
                    self._setup_animation()
                else:
                    self.blink_timer.start(1000)
        
        self._update_style()
    
    def set_message(self, message: str):
        """设置消息"""
        self.message = message
        if hasattr(self, 'message_label'):
            self.message_label.setText(message)
    
    def paintEvent(self, event):
        """绘制事件"""
        super().paintEvent(event)
        
        # 应用透明度
        if self._opacity < 1.0:
            painter = QPainter(self)
            painter.setOpacity(self._opacity)


class StatusLight(QWidget):
    """状态指示灯"""
    
    def __init__(self, status: StatusType = StatusType.NORMAL, parent=None):
        super().__init__(parent)
        self.status = status
        self.setFixedSize(16, 16)
        
        # 连接主题变化信号
        theme_manager.theme_changed.connect(self.update)
    
    def set_status(self, status: StatusType):
        """设置状态"""
        self.status = status
        self.update()
    
    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        colors = theme_manager.get_colors()
        
        # 根据状态选择颜色
        status_colors = {
            StatusType.NORMAL: colors['SUCCESS'],
            StatusType.WARNING: colors['WARNING'],
            StatusType.ERROR: colors['ERROR'],
            StatusType.INFO: colors['INFO'],
            StatusType.OFFLINE: colors['TEXT_DISABLED']
        }
        
        color = QColor(status_colors[self.status])
        
        # 绘制外圈
        painter.setPen(QPen(color.darker(120), 2))
        painter.setBrush(QBrush(color))
        
        # 绘制圆形指示灯
        painter.drawEllipse(2, 2, 12, 12)
        
        # 绘制内部高光
        highlight_color = QColor(color.lighter(150))
        highlight_color.setAlpha(100)
        painter.setBrush(QBrush(highlight_color))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(4, 4, 4, 4)


class AlarmIndicator(StatusIndicator):
    """报警指示器"""
    
    def __init__(self, alarm_type: str = "", alarm_time: str = "", 
                 alarm_depth: str = "", parent=None):
        self.alarm_type = alarm_type
        self.alarm_time = alarm_time
        self.alarm_depth = alarm_depth
        
        # 构建消息
        message = f"{alarm_type}"
        if alarm_time:
            message += f" | {alarm_time}"
        if alarm_depth:
            message += f" | {alarm_depth}"
        
        super().__init__(
            title=tr("alarm_warning", "⚠ 报警"),
            status=StatusType.WARNING,
            message=message,
            show_animation=True,
            parent=parent
        )
    
    def set_alarm_info(self, alarm_type: str, alarm_time: str = "", alarm_depth: str = ""):
        """设置报警信息"""
        self.alarm_type = alarm_type
        self.alarm_time = alarm_time
        self.alarm_depth = alarm_depth
        
        # 更新消息
        message = f"{alarm_type}"
        if alarm_time:
            message += f" | {alarm_time}"
        if alarm_depth:
            message += f" | {alarm_depth}"
        
        self.set_message(message)
