# 导入必要的模块
from PyQt5.QtWidgets import QApplication, QMainWindow, QFrame, QLabel, QVBoxLayout, QWidget

# 创建应用程序和主窗口
app = QApplication([])
window = QMainWindow()

# 创建一个QFrame作为容器
frame = QFrame()

# 创建两个QLabel
label1 = QLabel("标签1")
label2 = QLabel("标签2")

# 创建一个垂直布局
layout = QVBoxLayout()

# 将QLabel添加到布局中
layout.addWidget(label1)
layout.addWidget(label2)

# 设置QFrame的布局为垂直布局
frame.setLayout(layout)

# 设置QFrame的边框样式
frame.setFrameShape(QFrame.Box)
frame.setLineWidth(2)  # 设置边框宽度

# 添加QFrame到主窗口
window.setCentralWidget(frame)

# 显示主窗口
window.show()
app.exec_()
