# -*- coding: utf-8 -*-
"""
现代化滑块组件
支持主题切换和响应式布局
"""

# 标准库
from typing import Optional

# 第三方库
from PySide6.QtWidgets import QWidget, QSlider, QLabel, QVBoxLayout, QHBoxLayout
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

# 本地库
from common.style.theme_manager import theme_manager
from common.style.responsive_layout import responsive_layout
from common.i18n.i18n_manager import tr


class ModernSlider(QWidget):
    """现代化滑块组件"""
    
    # 值变化信号
    valueChanged = Signal(int)
    
    def __init__(self, title: str = "", min_val: int = 0, max_val: int = 100, 
                 initial_val: int = 50, unit: str = "", orientation=Qt.Orientation.Horizontal, parent=None):
        super().__init__(parent)
        
        self.title = title
        self.min_val = min_val
        self.max_val = max_val
        self.unit = unit
        self.orientation = orientation
        
        # 初始化UI
        self._init_ui()
        
        # 设置初始值
        self.slider.setValue(initial_val)
        self._update_value_label(initial_val)
        
        # 连接信号
        self.slider.valueChanged.connect(self._on_value_changed)
        
        # 连接主题变化信号
        theme_manager.theme_changed.connect(self._update_style)
        responsive_layout.screen_size_changed.connect(self._update_layout)
        
        # 应用初始样式
        self._update_style()
    
    def _init_ui(self):
        """初始化UI"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(16, 12, 16, 12)
        main_layout.setSpacing(responsive_layout.get_spacing())

        # 创建卡片容器
        self.card_frame = QWidget()
        self.card_frame.setProperty("cardStyle", "slider")
        card_layout = QVBoxLayout(self.card_frame)
        card_layout.setContentsMargins(20, 16, 20, 16)
        card_layout.setSpacing(12)

        # 标题和当前值的水平布局
        if self.title:
            header_layout = QHBoxLayout()

            # 标题标签
            self.title_label = QLabel(self.title)
            self.title_label.setProperty("labelType", "title")
            header_layout.addWidget(self.title_label)

            header_layout.addStretch()

            # 当前值显示（右对齐）
            self.value_label = QLabel(f"{self.min_val}{self.unit}")
            self.value_label.setProperty("labelType", "value")
            self.value_label.setAlignment(Qt.AlignmentFlag.AlignRight)
            header_layout.addWidget(self.value_label)

            card_layout.addLayout(header_layout)

        # 滑块容器
        slider_container = QWidget()
        slider_layout = QVBoxLayout(slider_container)
        slider_layout.setContentsMargins(0, 8, 0, 8)

        # 滑块
        self.slider = QSlider(Qt.Orientation.Horizontal)
        self.slider.setMinimum(self.min_val)
        self.slider.setMaximum(self.max_val)
        self.slider.setMinimumHeight(40)  # 增加滑块高度
        slider_layout.addWidget(self.slider)

        # 刻度标签布局
        scale_layout = QHBoxLayout()
        scale_layout.setContentsMargins(0, 4, 0, 0)

        # 最小值标签
        self.min_label = QLabel(str(self.min_val))
        self.min_label.setProperty("labelType", "scale")
        scale_layout.addWidget(self.min_label)

        scale_layout.addStretch()

        # 中间值标签
        mid_val = (self.min_val + self.max_val) // 2
        self.mid_label = QLabel(str(mid_val))
        self.mid_label.setProperty("labelType", "scale")
        self.mid_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        scale_layout.addWidget(self.mid_label)

        scale_layout.addStretch()

        # 最大值标签
        self.max_label = QLabel(str(self.max_val))
        self.max_label.setProperty("labelType", "scale")
        self.max_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        scale_layout.addWidget(self.max_label)

        slider_layout.addLayout(scale_layout)
        card_layout.addWidget(slider_container)

        main_layout.addWidget(self.card_frame)
    
    def _on_value_changed(self, value: int):
        """值变化处理"""
        self._update_value_label(value)
        self.valueChanged.emit(value)
    
    def _update_value_label(self, value: int):
        """更新数值标签"""
        self.value_label.setText(f"{value}{self.unit}")
    
    def _update_style(self):
        """更新样式"""
        colors = theme_manager.get_colors()

        # 滑块样式 - 更现代化的设计
        slider_style = f"""
        QSlider::groove:horizontal {{
            border: none;
            height: 6px;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['SECONDARY_BG']}, stop:1 {colors['BORDER']});
            border-radius: 3px;
        }}

        QSlider::handle:horizontal {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['PRIMARY_LIGHT']}, stop:1 {colors['PRIMARY']});
            border: 2px solid {colors['PRIMARY_DARK']};
            width: 24px;
            height: 24px;
            border-radius: 14px;
            margin: -10px 0;
            box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
        }}

        QSlider::handle:horizontal:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 {colors['PRIMARY_LIGHT']});
            border: 2px solid {colors['PRIMARY']};
            transform: scale(1.1);
        }}

        QSlider::handle:horizontal:pressed {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['PRIMARY']}, stop:1 {colors['PRIMARY_DARK']});
            border: 3px solid {colors['PRIMARY_DARK']};
        }}

        QSlider::sub-page:horizontal {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['PRIMARY']}, stop:1 {colors['PRIMARY_LIGHT']});
            border-radius: 3px;
        }}

        QSlider::add-page:horizontal {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['SECONDARY_BG']}, stop:1 {colors['BORDER']});
            border-radius: 3px;
        }}
        """

        # 卡片容器样式
        card_style = f"""
        QWidget[cardStyle="slider"] {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['CARD_BG']}, stop:1 rgba(255, 255, 255, 0.95));
            border: 1px solid {colors['BORDER']};
            border-radius: 12px;
            box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
        }}
        """

        self.slider.setStyleSheet(slider_style)
        self.card_frame.setStyleSheet(card_style)
    
    def _update_layout(self, screen_size):
        """更新布局"""
        self._update_style()
    
    def setValue(self, value: int):
        """设置值"""
        self.slider.setValue(value)
    
    def getValue(self) -> int:
        """获取值"""
        return self.slider.value()
    
    def setRange(self, min_val: int, max_val: int):
        """设置范围"""
        self.min_val = min_val
        self.max_val = max_val
        self.slider.setMinimum(min_val)
        self.slider.setMaximum(max_val)
        self.min_label.setText(str(min_val))
        self.max_label.setText(str(max_val))
        self.slider.setTickInterval((max_val - min_val) // 10)
    
    def setEnabled(self, enabled: bool):
        """设置启用状态"""
        super().setEnabled(enabled)
        self.slider.setEnabled(enabled)


class TorqueSlider(ModernSlider):
    """扭矩滑块 - 专门用于扭矩控制"""
    
    def __init__(self, title: str = "", parent=None):
        super().__init__(
            title=title,
            min_val=0,
            max_val=100,
            initial_val=50,
            unit="%",
            orientation=Qt.Orientation.Horizontal,
            parent=parent
        )
        
        # 设置扭矩特定的样式
        self._setup_torque_style()
    
    def _setup_torque_style(self):
        """设置扭矩特定样式"""
        # 可以在这里添加扭矩滑块特有的样式或行为
        pass
