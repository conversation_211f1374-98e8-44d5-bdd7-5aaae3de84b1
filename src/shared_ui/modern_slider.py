# -*- coding: utf-8 -*-
"""
现代化滑块组件
支持主题切换和响应式布局
"""

# 标准库
from typing import Optional

# 第三方库
from PySide6.QtWidgets import QWidget, QSlider, QLabel, QVBoxLayout, QHBoxLayout
from PySide6.QtCore import Qt, Signal, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QPainter, QPen, QColor, QFont

# 本地库
from common.style.theme_manager import theme_manager
from common.style.responsive_layout import responsive_layout
from common.i18n.i18n_manager import tr


class ModernSlider(QWidget):
    """现代化滑块组件"""
    
    # 值变化信号
    valueChanged = Signal(int)
    
    def __init__(self, title: str = "", min_val: int = 0, max_val: int = 100, 
                 initial_val: int = 50, unit: str = "", orientation=Qt.Orientation.Horizontal, parent=None):
        super().__init__(parent)
        
        self.title = title
        self.min_val = min_val
        self.max_val = max_val
        self.unit = unit
        self.orientation = orientation
        
        # 初始化UI
        self._init_ui()
        
        # 设置初始值
        self.slider.setValue(initial_val)
        self._update_value_label(initial_val)
        
        # 连接信号
        self.slider.valueChanged.connect(self._on_value_changed)
        
        # 连接主题变化信号
        theme_manager.theme_changed.connect(self._update_style)
        responsive_layout.screen_size_changed.connect(self._update_layout)
        
        # 应用初始样式
        self._update_style()
    
    def _init_ui(self):
        """初始化UI"""
        if self.orientation == Qt.Orientation.Horizontal:
            main_layout = QVBoxLayout(self)
        else:
            main_layout = QHBoxLayout(self)
        
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(responsive_layout.get_spacing())
        
        # 标题标签
        if self.title:
            self.title_label = QLabel(self.title)
            self.title_label.setProperty("labelType", "title")
            self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            main_layout.addWidget(self.title_label)
        
        # 滑块和数值布局
        if self.orientation == Qt.Orientation.Horizontal:
            slider_layout = QHBoxLayout()
        else:
            slider_layout = QVBoxLayout()
        
        # 最小值标签
        self.min_label = QLabel(str(self.min_val))
        self.min_label.setProperty("labelType", "small")
        slider_layout.addWidget(self.min_label)
        
        # 滑块
        self.slider = QSlider(self.orientation)
        self.slider.setMinimum(self.min_val)
        self.slider.setMaximum(self.max_val)
        self.slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.slider.setTickInterval((self.max_val - self.min_val) // 10)
        slider_layout.addWidget(self.slider, 1)
        
        # 最大值标签
        self.max_label = QLabel(str(self.max_val))
        self.max_label.setProperty("labelType", "small")
        slider_layout.addWidget(self.max_label)
        
        main_layout.addLayout(slider_layout)
        
        # 当前值显示
        self.value_label = QLabel(f"{self.min_val}{self.unit}")
        self.value_label.setProperty("labelType", "large")
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(self.value_label)
    
    def _on_value_changed(self, value: int):
        """值变化处理"""
        self._update_value_label(value)
        self.valueChanged.emit(value)
    
    def _update_value_label(self, value: int):
        """更新数值标签"""
        self.value_label.setText(f"{value}{self.unit}")
    
    def _update_style(self):
        """更新样式"""
        colors = theme_manager.get_colors()
        
        # 滑块样式
        slider_style = f"""
        QSlider::groove:horizontal {{
            border: 1px solid {colors['BORDER']};
            height: 8px;
            background: {colors['SECONDARY_BG']};
            border-radius: 4px;
        }}
        
        QSlider::handle:horizontal {{
            background: {colors['PRIMARY']};
            border: 2px solid {colors['PRIMARY_DARK']};
            width: 20px;
            height: 20px;
            border-radius: 12px;
            margin: -8px 0;
        }}
        
        QSlider::handle:horizontal:hover {{
            background: {colors['PRIMARY_LIGHT']};
        }}
        
        QSlider::handle:horizontal:pressed {{
            background: {colors['PRIMARY_DARK']};
        }}
        
        QSlider::sub-page:horizontal {{
            background: {colors['PRIMARY']};
            border-radius: 4px;
        }}
        
        QSlider::add-page:horizontal {{
            background: {colors['SECONDARY_BG']};
            border-radius: 4px;
        }}
        
        QSlider::groove:vertical {{
            border: 1px solid {colors['BORDER']};
            width: 8px;
            background: {colors['SECONDARY_BG']};
            border-radius: 4px;
        }}
        
        QSlider::handle:vertical {{
            background: {colors['PRIMARY']};
            border: 2px solid {colors['PRIMARY_DARK']};
            width: 20px;
            height: 20px;
            border-radius: 12px;
            margin: 0 -8px;
        }}
        
        QSlider::handle:vertical:hover {{
            background: {colors['PRIMARY_LIGHT']};
        }}
        
        QSlider::handle:vertical:pressed {{
            background: {colors['PRIMARY_DARK']};
        }}
        
        QSlider::sub-page:vertical {{
            background: {colors['PRIMARY']};
            border-radius: 4px;
        }}
        
        QSlider::add-page:vertical {{
            background: {colors['SECONDARY_BG']};
            border-radius: 4px;
        }}
        """
        
        self.slider.setStyleSheet(slider_style)
    
    def _update_layout(self, screen_size):
        """更新布局"""
        self._update_style()
    
    def setValue(self, value: int):
        """设置值"""
        self.slider.setValue(value)
    
    def getValue(self) -> int:
        """获取值"""
        return self.slider.value()
    
    def setRange(self, min_val: int, max_val: int):
        """设置范围"""
        self.min_val = min_val
        self.max_val = max_val
        self.slider.setMinimum(min_val)
        self.slider.setMaximum(max_val)
        self.min_label.setText(str(min_val))
        self.max_label.setText(str(max_val))
        self.slider.setTickInterval((max_val - min_val) // 10)
    
    def setEnabled(self, enabled: bool):
        """设置启用状态"""
        super().setEnabled(enabled)
        self.slider.setEnabled(enabled)


class TorqueSlider(ModernSlider):
    """扭矩滑块 - 专门用于扭矩控制"""
    
    def __init__(self, title: str = "", parent=None):
        super().__init__(
            title=title,
            min_val=0,
            max_val=100,
            initial_val=50,
            unit="%",
            orientation=Qt.Orientation.Horizontal,
            parent=parent
        )
        
        # 设置扭矩特定的样式
        self._setup_torque_style()
    
    def _setup_torque_style(self):
        """设置扭矩特定样式"""
        # 可以在这里添加扭矩滑块特有的样式或行为
        pass
