from PyQt5.QtWidgets import QApplication, QMainWindow, QMdiArea, QMdiSubWindow, QTextEdit

app = QApplication([])

# 创建主窗口
main_window = QMainWindow()
main_window.setWindowTitle("多文档界面示例")

# 创建QMdiArea作为主窗口的中央部件
mdi_area = QMdiArea()
main_window.setCentralWidget(mdi_area)

# 创建多个内嵌窗口
for i in range(1, 4):
    sub_window = QMdiSubWindow()
    sub_window.setWindowTitle(f"文档{i}")
    text_edit = QTextEdit()
    sub_window.setWidget(text_edit)
    mdi_area.addSubWindow(sub_window)

main_window.show()
app.exec_()
