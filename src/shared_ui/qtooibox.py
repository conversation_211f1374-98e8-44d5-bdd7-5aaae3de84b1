# 导入必要的模块
from PySide6.QtWidgets import QApplication, QMainWindow, QToolBox, QLabel, QPushButton,QWidget,QVBoxLayout

# 创建应用程序和主窗口
app = QApplication([])
window = QMainWindow()

# 创建一个QToolBox
toolbox = QToolBox()

# 创建第一个选项卡
tab1 = QWidget()
label1 = QLabel("这是第一个选项卡的内容")
tab1.layout = QVBoxLayout()
tab1.layout.addWidget(label1)
tab1.setLayout(tab1.layout)
toolbox.addItem(tab1, "选项卡1")

# 创建第二个选项卡
tab2 = QWidget()
label2 = QLabel("这是第二个选项卡的内容")
button = QPushButton("按钮")
tab2.layout = QVBoxLayout()
tab2.layout.addWidget(label2)
tab2.layout.addWidget(button)
tab2.setLayout(tab2.layout)
toolbox.addItem(tab2, "选项卡2")

# 添加QToolBox到主窗口
window.setCentralWidget(toolbox)

# 显示主窗口
window.show()
app.exec_()
