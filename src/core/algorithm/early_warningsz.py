from collections import deque
import configparser
import csv
from datetime import datetime
import multiprocessing
import os
import queue
import threading  
import numpy as np
from multiprocessing import Queue,Process
# 导入日志监控
import config.globals as overall

# 张力 和 速度 融合判断遇阻遇卡点
class Earlywarning(Process):
    '''
    算法进程
    '''
    def __init__(self, IS_COMPUTE,SPEED_LIMIT,TENSION_LIMIT,blue_data:Queue,winch_data:Queue,warning:Queue):
        super().__init__()
        print("张力算法进程启动")
        self.blue_data = blue_data
        '''蓝牙数据'''

        self.winch_data = winch_data
        '''绞车数据'''

        self.warning= warning

        self.SPEED_LIMIT = SPEED_LIMIT
        self.TENSION_LIMIT = TENSION_LIMIT
        self.IS_COMPUTE = IS_COMPUTE

        self.speed_change_rate = float(os.environ.get('SPEED_CHANGE_RATE'))  # 速度差值比
        self.tension_change_rate = float(os.environ.get('TENSION_CHANGE_RATE')) # 张力差值比
        self.list_length = int(os.environ.get("LIST_LENGTH"))  # 预警计算点数
        self.tension_change = float(os.environ.get("TENSION_CHANGE"))  # 张力差值

    def run(self):
        self.calculate_tension()
        

    def calculate_tension(self)->None:
        '''
        - introduce : 绞车数据算法判断
        '''
        def one_judge(data:dict):
            '''第一次判断'''
            speed_list = data['speed_list']
            
            if abs(speed_list[0]-speed_list[-1])>abs(speed_list[0]*self.speed_change_rate):
                return True
            else:
                return False

        def two_judge(data:dict):
            '''二次判断'''
            speed_list = data['speed_list']
            speed = data['speed']
            tension_list = data['tension_list']
            tension = data['tension']
            for i in range(len(speed_list)-1):
                if speed_list[i]<speed_list[i+1]:
                    return False

            if speed_list[-1]<speed:
                return False
            for i in range(len(tension_list)-1):
                if tension_list[i]<tension_list[i+1]:
                    return False
            if tension_list[-1]<tension:
                return False   
            # if abs(tension_list[0]-tension_list[-1])>self.tension_change:
            if abs(tension_list[0]-tension_list[-1])>abs(tension_list[0]*self.tension_change_rate):
                return True

        TENSION = deque(maxlen=self.list_length)   
        SPEED = deque(maxlen=self.list_length)
        DEPTH = deque(maxlen=self.list_length)

        while True:
            # print(self.IS_COMPUTE.value)
            if len(TENSION) == self.list_length:
                if not self.winch_data.empty():
                    data = self.winch_data.get()
                    speed = float(data['speed'])
                    tension = float(data['tension'])
                    
                    # 速度预警
                    if speed > self.SPEED_LIMIT.value:
                        current_time = str(datetime.now().strftime('%H:%M:%S'))
                        self.write_csv({
                            "tension_list":list(TENSION),
                            "speed_list":list(SPEED),
                            "depth_list":list(DEPTH), 
                            'warning':current_time+"_"+str(data['tension'])+"_"+str(data['speed'])+"_"+str(data['depth'])+"_超速"
                        })
                        try:
                            if self.IS_COMPUTE.value:
                                self.warning.put({"data":f'{current_time}'+"_"+str(data['tension'])+"_超速"}, block=False)
                        except queue.Full:
                            pass
                        except Exception as e:
                            print(f"放入数据时发生错误：{e}")

                    # 张力预警
                    if tension > self.TENSION_LIMIT.value:
                        current_time = str(datetime.now().strftime('%H:%M:%S'))
                        self.write_csv({
                            "tension_list":list(TENSION),
                            "speed_list":list(SPEED),
                            "depth_list":list(DEPTH), 
                            'warning':current_time+"_"+str(data['tension'])+"_"+str(data['speed'])+"_"+str(data['depth'])+"_张力过大"
                        })
                        try:
                            if self.IS_COMPUTE.value:
                                self.warning.put({"data":f'{current_time}'+"_"+str(data['tension'])+"_张力过大"}, block=False)
                        except queue.Full:
                            pass
                        except Exception as e:
                            print(f"放入数据时发生错误：{e}")
                    
                    # 遇阻
                    # if all(i > 0 for i in list(SPEED)) and data['speed']>0:
                    # if data['speed'] > 0:
                    if list(SPEED)[0] > 0:
                        is_bool = one_judge({
                            "speed_list":list(SPEED),
                        })

                        if is_bool:
                            current_time = str(datetime.now().strftime('%H:%M:%S'))
                            self.write_csv({
                                "tension_list":list(TENSION),
                                "speed_list":list(SPEED),
                                "depth_list":list(DEPTH), 
                                'warning':current_time+"_"+str(data['tension'])+"_"+str(data['speed'])+"_"+str(data['depth'])+"_遇阻"                               
                            })
                            try:
                                if self.IS_COMPUTE.value:
                                    self.warning.put({"data":f'{current_time}'+"_"+str(data['tension'])+"_遇阻"}, block=False)
                                    TENSION.clear() 
                                    SPEED.clear() 
                                    DEPTH.clear() 
                            except queue.Full:
                                pass
                            except Exception as e:
                                print(f"放入数据时发生错误：{e}")                            
                        else:
                            is_bool=two_judge({
                                "speed_list":list(SPEED),
                                "speed":data['speed'],
                                'tension_list':list(TENSION),
                                'tension':data['tension']
                            })
                            if is_bool:
                                current_time = str(datetime.now().strftime('%H:%M:%S'))
                                self.write_csv({
                                    "tension_list":list(TENSION),
                                    "speed_list":list(SPEED),
                                    "depth_list":list(DEPTH), 
                                    'warning':current_time+"_"+str(data['tension'])+"_"+str(data['speed'])+"_"+str(data['depth'])+"_遇阻"                               
                                })
                                try:
                                    if self.IS_COMPUTE.value:
                                        self.warning.put({"data":f'{current_time}'+"_"+str(data['tension'])+"_遇阻"}, block=False)
                                        TENSION.clear() 
                                        SPEED.clear() 
                                        DEPTH.clear() 
                                except queue.Full:
                                    pass
                                except Exception as e:
                                    print(f"放入数据时发生错误：{e}")                                  
                    else:
                        #遇卡
                        # if all(i < 0 for i in list(SPEED)) and data['speed']<0:
                        # if data['speed'] < 0:
                        if list(SPEED)[0] < 0:
                            is_bool = one_judge({
                                "speed_list":list(SPEED),
                            })

                            if is_bool:                      
                                current_time = str(datetime.now().strftime('%H:%M:%S'))
                                self.write_csv({
                                    "tension_list":list(TENSION),
                                    "speed_list":list(SPEED),
                                    "depth_list":list(DEPTH), 
                                    'warning':current_time+"_"+str(data['tension'])+"_"+str(data['speed'])+"_"+str(data['depth'])+"_遇卡"                               
                                })
                                try:
                                    if self.IS_COMPUTE.value:
                                        self.warning.put({"data":f'{current_time}'+"_"+str(data['tension'])+"_遇卡"}, block=False)
                                        TENSION.clear() 
                                        SPEED.clear() 
                                        DEPTH.clear() 
                                except queue.Full:
                                    pass
                                except Exception as e:
                                    print(f"放入数据时发生错误：{e}")                                  
                            else:
                                is_bool=two_judge({
                                    "speed_list":list(SPEED),
                                    "speed":data['speed'],
                                    'tension_list':list(TENSION),
                                    'tension':data['tension']
                                })  
                                if is_bool:
                                    current_time = str(datetime.now().strftime('%H:%M:%S'))
                                    self.write_csv({
                                        "tension_list":list(TENSION),
                                        "speed_list":list(SPEED),
                                        "depth_list":list(DEPTH), 
                                        'warning':current_time+"_"+str(data['tension'])+"_"+str(data['speed'])+"_"+str(data['depth'])+"_遇卡"                               
                                    })
                                    try:
                                        if self.IS_COMPUTE.value:
                                            self.warning.put({"data":f'{current_time}'+"_"+str(data['tension'])+"_遇卡"}, block=False)
                                            TENSION.clear() 
                                            SPEED.clear() 
                                            DEPTH.clear() 
                                    except queue.Full:
                                        pass
                                    except Exception as e:
                                        print(f"放入数据时发生错误：{e}")                                                    
                    TENSION.append(data['tension'])
                    SPEED.append(data['speed'])
                    DEPTH.append(data['depth'])
            else:
                if not self.winch_data.empty():  
                    data = self.winch_data.get()                
                    TENSION.append(data['tension'])
                    SPEED.append(data['speed'])
                    DEPTH.append(data['depth'])
            

    def write_csv(self,warning:dict):
        '将报警信息写入到csv'
        tension_list:list = warning['tension_list']
        speed_list:list = warning['speed_list']
        depth_list:list = warning['depth_list']
        warning_list:str = warning['warning']
        write_list =[]
        for i in range(self.list_length):
            write_list.append(["",tension_list[i],speed_list[i],depth_list[i]])
        data_list:list = warning_list.split('_')
        current_time = str(datetime.now().strftime('%Y-%m-%d'))
        directory = "data/Abnormal"
        if not os.path.exists(directory):
            os.makedirs(directory)
        file_path = os.path.join("data",'Abnormal',current_time + "-JC-Warning.csv")  
        if not os.path.exists(file_path):  
            with open(file_path, 'w', newline='') as file:
                writer = csv.writer(file)
                writer.writerow(['time','tension','speed','depth','%','illustrate'])
        with open(file_path, 'a', newline='') as file:
            for warn in write_list:
                writer = csv.writer(file)
                writer.writerow(warn)
            writer = csv.writer(file)
            writer.writerow(data_list)   




