# /usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2025/7/28 10:28
# <AUTHOR> lx
# @Email   : <EMAIL>
# @File    : wifi_acme.py
# @Software: Visual Studio Code


# 字节数据的处理 来自ACME 设备

from PySide6.QtCore import QObject, Signal, QTime   # 导入QTime 用于时间处理 
import socket 
import os
import threading
import time
import struct
import csv
from datetime import datetime 

socket_ip = "*************"
socket_port = 7278

class WifiAcme(QObject):
    # 继承QObject 的类才能使用Qt中的信号与槽机制，这样才能将解析后的数据传输给Qt组件
    def __init__(self,url):
        super().__init__() # 调用父类的构造函数
        self.is_connected = False # 连接状态
        self.time = QTime()
        # self.time.start() # 启动计时器
        self.signal = Signal(str)

        self.tension_correction_factor = 4.6 # 添加张力矫正因子（除以4.5-4.6）

        self.keep_analyzing = True # 分析线程

        self.all_curve_name = []
        self.data_points = {
            "depth": [],
            "speed": [],
            "tension": []
        }    # 曲线点的数据

        self.save_to_csv = True
    def connectz(self):
        """连接到TCP服务器"""
        try:
            self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM) # 创建socket对象 AF_INET 表示IPv4 协议 SOCK_STREAM 表示TCP协议   IPv6 是AF_INET6
            self.client_socket.connect((socket_ip, socket_port)) # 连接服务器 创建连接
            self.client_socket.settimeout(10) # 设置超时时间10s
            self.client_socket.setblocking(False) # 设置为非阻塞模式
            self.is_connected = True # 连接成功
        except Exception as e:
            print(f"连接失败: {e}")
            return False
        return True
    
    def satrt(self):
        """启动数据接收线程,解析数据"""
        if not self.is_connected and not self.connectz():
            return False   # 检查连接
        
        self.threads = threading.Thread(target=self.receive_data) # 启动解析数据线程
        self.threads.daemon = True
        self.threads.start()
        
        # 启动分析线程
        self.analysis_thread = threading.Thread(target=self.analyze_data) # 分析线程
        self.analysis_thread.daemon = True
        self.analysis_thread.start()

        return True
    
    ### 数据处理线程
    def receive_data(self):
        """接收并处理数据   这是解析acme 发送的数据  每秒发送200多个点 一个点有800多个字节"""
        file_list = []  # 大包头数据
        is_one = True   # 在接收大包头时为 True
        is_two = True   # 在大包头接收完毕后为 False
        file_size = 0
        
        try:
            while self.is_connected:
                get_data = self.client_socket.recv(50000) # 获取发过来的字节数据
                if not get_data:
                    break
                
                # 解析数据,转化为数组,每两个字符为一个字节,每个字节为一组
                hex_data = get_data.hex() # hex 
                hex_data_list = [int(hex_data[i:i + 2], 16) for i in range(0, len(hex_data), 2)]
                
                # 将包头文件写入file_list
                if is_two:
                    file_list += hex_data_list

                # 查看大包的长度
                if len(file_list) >= 34 and is_one:
                    # 获取大包数据的长度
                    file_size = int.from_bytes(bytes(file_list[30:32]), byteorder="little")
                    is_one = False

                # 如果大包数据已全部获得,执行
                elif len(file_list) >= file_size + 34 and is_two:
                    # 将多出来的数据(数据包)另存到share_list
                    self.share_list += file_list[file_size + 34:]

                    # 解析大包里的数据
                    uToolInfoPt = self.get_uToolInfoPt(all_data=file_list[34:])  # 获取仪器信息起始块位置
                    sToolNum = self.get_sToolNum(all_data=file_list[34:]) # 获取仪器数
                    # 获取所有曲线名               
                    self.all_curve_name = self.cover_tools_info(file_list[34:], uToolInfoPt, sToolNum)  # 获取仪器曲线数
                    
                    # 查找需要的曲线索引
                    num = 0
                    for curve_name in self.all_curve_name:
                        # 查找深度、速度和张力曲线
                        # 使用更灵活的匹配方式
                        if "DEPTH" in curve_name:
                            self.depth_line = num
                            print(f"找到深度曲线: {curve_name}, 索引: {num}")
                        elif "SPD" in curve_name:
                            self.speed_line = num
                            print(f"找到速度曲线: {curve_name}, 索引: {num}")
                        elif "TEN_CABLE" in curve_name:
                            self.tension_line = num
                            print(f"找到张力曲线: {curve_name}, 索引: {num}")
                        num += 1
                    
                    is_two = False
                    # 打印找到的所有曲线名称以便诊断
                    print("找到的所有曲线:", self.all_curve_name)
                    continue

                # 以下全是数据包的处理
                if not is_two:
                    self.share_list += hex_data_list
                    
        except socket.timeout:
            print("连接超时")
            self.disconnect()
        except ConnectionResetError:
            print("连接被重置")
            self.disconnect()
        except Exception as e:
            print(f"接收数据时出错: {e}")
            self.disconnect()
    
    def get_uToolInfoPt(self, all_data):
        """获取仪器信息块起始位置"""
        uToolInfoPt = int.from_bytes(bytes(all_data[14:18]), byteorder='little')
        return uToolInfoPt

    def get_sToolNum(self, all_data):
        """获取仪器数"""
        sToolNum = int.from_bytes(bytes(all_data[142:144]), byteorder="little")
        return sToolNum

    def get_uCurveNum(self, all_data, tool_pt):
        """获取仪器曲线数"""
        uCurveNum = int.from_bytes(bytes(all_data[(tool_pt + 419):(tool_pt + 423)]), byteorder="little")
        return uCurveNum

    def get_curve_name(self, all_data, tool_pt):
        """获取曲线名"""
        curve_name = "".join([chr(hex_char) for hex_char in all_data[tool_pt + 427:tool_pt + 427 + 16]])
        return curve_name.strip('\x00')  # 移除空字符以便更清晰地打印
    
    def cover_tools_info(self, all_data, uToolInfoPt, sToolNum):
        """遍历仪器信息获取曲线名队列"""
        all_curve_name = []
        all_curve_num = 0
        
        for num in range(0, sToolNum):
            if num == 0:
                curve_num = self.get_uCurveNum(all_data=all_data, tool_pt=uToolInfoPt)
                if curve_num == 0:
                    continue

                for c_num in range(0, curve_num):
                    if c_num == 0:
                        all_curve_name.append(self.get_curve_name(all_data, uToolInfoPt))
                    else:
                        all_curve_name.append(self.get_curve_name(all_data, uToolInfoPt + all_curve_num * 202))
                    all_curve_num += 1

            else:
                curve_num = self.get_uCurveNum(all_data=all_data, tool_pt=uToolInfoPt + (num * 427) + all_curve_num * 202)
                if curve_num == 0:
                    continue

                for c_num in range(0, curve_num):
                    if c_num == 0:
                        all_curve_name.append(self.get_curve_name(all_data, uToolInfoPt + num * 427 + all_curve_num * 202))
                    else:
                        all_curve_name.append(self.get_curve_name(all_data, uToolInfoPt + num * 427 + all_curve_num * 202))
                    all_curve_num += 1
        return all_curve_name

    def calibrate_tension(self, value):
        """根据真实数据校准张力值"""
        # 如果需要更复杂的校准，可以在这里实现
        return value / self.tension_correction_factor
    ###

    ### 分析线程
    def analyze_data(self):
        """分析接收到的数据"""
        data_size = None
        number = 0
        
        while self.keep_analyzing:
            if len(self.share_list) >= 34:
                # 解析数据体的长度
                try:
                    data_size = int.from_bytes(bytes(self.share_list[30:32]), byteorder="little")
                    
                    if len(self.share_list) >= data_size + 34:
                        # 获取一整个数据包(包头+数据体)
                        change_data = self.share_list[:(data_size + 34)]
                        
                        if len(change_data) >= data_size + 34:
                            # 解析数据包
                            data_block = change_data[34:]
                            precise_data = self.parse_data_block(data_block)
                            
                            # 确保我们找到了所有需要的曲线
                            if len(precise_data) > max(self.depth_line, self.speed_line, self.tension_line):
                                # 提取深度、速度和张力数据
                                depth = precise_data[self.depth_line]
                                speed = precise_data[self.speed_line]
                                # 应用张力校正因子
                                raw_tension = precise_data[self.tension_line]
                                tension = raw_tension / self.tension_correction_factor
                                

                                # 调整速度正负值
                                if depth < self.depth_last:
                                    speed = -abs(speed)
                                else:
                                    speed = abs(speed)
                                self.depth_last = depth
                                
                                # 保存数据点
                                self.data_points["depth"].append((self.jc_sum, depth))
                                self.data_points["speed"].append((self.jc_sum, speed))
                                # self.data_points["tension"].append((self.jc_sum, tension))
                                self.data_points["tension"].append((self.jc_sum, raw_tension))
                                

                                # self.data_updated.emit(self.data_points) 添加槽函数获取解析后到数据
                                # 打印当前数据
                                #print(f"深度: {depth:.2f}m, 速度: {speed:.2f}m/min, 张力: {tension:.2f}kN (原始值: {raw_tension:.2f}kN), 索引: {self.jc_sum}")
                                # 保存到CSV
                                if self.save_to_csv:
                                    self.write_data([tension, speed, depth])
                                
                                self.jc_sum += 1
                            
                            # 删除已解析的数据包
                            del self.share_list[:data_size + 34]
                        
                        number += 1
                        
                        if number >= 100:
                            # 批量删除已解析的数据包
                            number = 0
                            del self.share_list[:(data_size + 34)*100]
                    
                except Exception as e:
                    print(f"分析数据时出错: {e}")
                    # 如果发生错误，丢弃当前数据包
                    if len(self.share_list) > 34:
                        del self.share_list[:34]
            # 短暂休眠减少CPU占用
            time.sleep(0.01)

    def parse_data_block(self, data_block):
        """解析数据块为浮点数列表"""
        num_floats = len(data_block) // 4
        
        try:
            # 一次性将数据块解析为浮点数列表
            float_numbers = struct.unpack(f'<{num_floats}f', bytes(data_block[:num_floats * 4]))
            # 对解析出的浮点数进行四舍五入
            precise_data = [round(num, 2) for num in float_numbers]
            return precise_data
        except Exception as e:
            print(f"解析数据块错误: {e}")
            return []
    
    def write_data(self, data_list):
        """将数据写入CSV文本文件"""
        current_time = str(datetime.now().strftime('%Y-%m-%d'))
        directory = "../Historical_data/JC_data"
        if not os.path.exists(directory):
            os.makedirs(directory)
        file_path = os.path.join("data", "JC_data", current_time + ".csv")
        
        # 如果文件不存在，写入表头
        if not os.path.exists(file_path):
            with open(file_path, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(['Date_Time', 'Tension', 'Speed', 'Depth'])

        # 追加数据
        with open(file_path, 'a', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            data_list = [str(datetime.now().strftime('%H:%M:%S'))] + data_list
            writer.writerow(data_list)
    ### 



    def disconnectz(self):
        """断开与设备的连接"""
        self.is_connected = False # 连接状态
        if self.socket:
            try:
                self.socket.close()
            except Exception as e:
                print(f"关闭套接字错误: {e}")
            self.socket = None


if "__name__" == "__main__":
    WifiAcme()






