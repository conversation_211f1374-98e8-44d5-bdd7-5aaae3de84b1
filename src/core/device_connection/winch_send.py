from PySide6.QtCore import QObject, Signal, QTimer
import numpy as np
import socket
import logging

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("SendWinch")
CMD_1 = np.zeros(65, dtype=np.uint8)
winch_ip = "*************"
winch_baud_rate = 7278

class SendWinch(QObject):
    """发送绞车控制指令的类"""
    command_sent = Signal(bool)  # 发送指令结果信号
    
    def __init__(self):
        super().__init__()
        self.is_connected = False
        self.client_socket = None
        self._init_command_buffer()
    
    def _init_command_buffer(self):
        """初始化指令缓冲区"""
        # 包头
        CMD_1[0] = 0x08
        CMD_1[1] = 0x00
        CMD_1[2] = 0x00
        CMD_1[3] = 0x07
        CMD_1[4] = 0x00

        CMD_1[5] = 0x80  # 使能卡关
        CMD_1[6] = 0x00  # 扭矩调节
        CMD_1[7] = 0x00  # 测井模式切换
        CMD_1[8] = 0x00  # 方向控制
        
        # 基础指令列表
        list_cmd = [0x00,0x00,0x00, 0x00, 0x08, 0x00, 0x00, 0x07, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0A, 0x85, 0x08, 0x00, 0x00, 0x07, 0x05]
        for i in range(22):
            CMD_1[9+i] = list_cmd[i]
            
        # 参数设置列表
        listcmd_list_2 = [0x11, 0x8E, 0x00, 0x64, 0x04, 0x63, 0x08, 0x00, 0x00, 0x07, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x64, 0x00, 0x14, 0x08, 0x00, 0x00, 0x07, 0x30, 0x00, 0xA6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]
        for i in range(32):
            CMD_1[i+33] = listcmd_list_2[i]

    def connect(self):
        """连接到TCP服务器"""
        try:
            if self.client_socket:
                self.disconnect()
                
            self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.client_socket.settimeout(5)  # 5秒超时
            self.client_socket.connect((winch_ip, winch_baud_rate))
            self.is_connected = True
            logger.info(f"已连接到TCP服务器 {winch_ip}:{winch_baud_rate}")
            return True
        except Exception as e:
            logger.error(f"TCP连接失败: {e}")
            self.is_connected = False
            return False

    def disconnect(self):
        """断开TCP连接"""
        if self.client_socket:
            try:
                self.client_socket.close()
            except Exception as e:
                logger.error(f"关闭TCP连接失败: {e}")
            finally:
                self.client_socket = None
                self.is_connected = False

    def send_command(self):
        """发送当前命令缓冲区的内容"""
        if not self.is_connected and not self.connect():
            return False
            
        try:
            self.client_socket.send(CMD_1)
            self.command_sent.emit(True)
            return True
        except Exception as e:
            logger.error(f"发送指令失败: {e}")
            self.is_connected = False
            self.command_sent.emit(False)
            return False

    # 控制指令方法
    def start_up(self):
        """启动绞车"""
        self._init_command_buffer()
        CMD_1[23] = 0x02  # 设置下放方向
        return self.send_command()

    def stop_now(self):
        """紧急停车"""
        self._init_command_buffer()
        CMD_1[23] = 0x00  # 停止
        CMD_1[31] = 0x00  # 速度清零
        CMD_1[32] = 0x00
        CMD_1[5] = 0xC0   # 紧急停车标志
        return self.send_command()

    def normal_stop(self):
        """正常停车"""
        self._init_command_buffer()
        CMD_1[23] = 0x00  # 停止
        CMD_1[31] = 0x00  # 速度清零
        CMD_1[32] = 0x00
        return self.send_command()

    def lift_up(self):
        """上提"""
        self._init_command_buffer()
        CMD_1[23] = 0x01  # 上提方向
        return self.send_command()

    def lower_down(self):
        """下放"""
        self._init_command_buffer()
        CMD_1[23] = 0x02  # 下放方向
        return self.send_command()

    def set_speed(self, speed):
        """设置速度
        
        Args:
            speed: 速度值 (0-3700 for normal mode, 0-1300 for special mode)
        """
        if speed < 0:
            speed = 0
            
        # 根据模式限制最大速度
        if CMD_1[8] == 0:  # 普通模式
            speed = min(speed, 3700)
        else:  # 特殊模式
            speed = min(speed, 1300)
            
        # 设置速度值
        speed_value = int(speed / 6)  # 转换为实际发送值
        CMD_1[31] = (speed_value & 0xFF00) >> 8
        CMD_1[32] = speed_value & 0xFF
        
        return self.send_command()

    def set_torque(self, increase=True):
        """设置扭矩
        
        Args:
            increase: True表示增大扭矩，False表示减小扭矩
        """
        self._init_command_buffer()
        CMD_1[6] = 0x01 if increase else 0x02
        return self.send_command()

    def reset_command(self):
        """重置命令缓冲区"""
        self._init_command_buffer()

    def load_csv_data(self, file_path, depth_col=0, speed_col=1, tension_col=2):
        """加载CSV数据
        
        Args:
            file_path: CSV文件路径
            depth_col: 深度列索引
            speed_col: 速度列索引
            tension_col: 张力列索引
        """
        try:
            import pandas as pd
            self.csv_data = pd.read_csv(file_path)
            self.csv_row_index = 1
            
            # 验证列索引
            if max(depth_col, speed_col, tension_col) >= len(self.csv_data.columns):
                raise ValueError("列索引超出范围")
                
            # 保存列索引
            self.depth_col = depth_col
            self.speed_col = speed_col
            self.tension_col = tension_col
            
            return True
        except Exception as e:
            print(f"加载CSV数据失败: {e}")
            return False
            
    def start_csv_simulation(self, interval=100):
        """开始CSV数据模拟
        
        Args:
            interval: 数据更新间隔(毫秒)
        """
        if self.csv_data is None:
            return False
            
        self.csv_timer = QTimer()
        self.csv_timer.timeout.connect(self._emit_csv_data)
        self.csv_timer.start(interval)
        return True
        
    def stop_csv_simulation(self):
        """停止CSV数据模拟"""
        if self.csv_timer:
            self.csv_timer.stop()
            
    def _emit_csv_data(self):
        """发送CSV数据"""
        if not self.csv_data is None and not self.paused:
            try:
                # 获取当前行数据
                row = self.csv_data.iloc[self.csv_row_index]
                
                # 创建数据点
                data_points = {
                    "depth": [(self.jc_sum, float(row[self.depth_col]))],
                    "speed": [(self.jc_sum, float(row[self.speed_col]))],
                    "tension": [(self.jc_sum, float(row[self.tension_col]))]
                }
                
                # 发送数据
                self.data_updated.emit(data_points)
                
                # 更新计数器
                self.jc_sum += 1
                self.csv_row_index = (self.csv_row_index + 1) % len(self.csv_data)
                
            except Exception as e:
                print(f"发送CSV数据失败: {e}")