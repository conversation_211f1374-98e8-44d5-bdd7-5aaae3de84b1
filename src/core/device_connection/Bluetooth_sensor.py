import asyncio
import csv
from datetime import datetime
import multiprocessing
import os
import queue
from bleak import BleakClient, BleakScanner
import bleak
from multiprocessing import Queue


### 蓝牙进程
class Bluetoothutil(multiprocessing.Process):  
    def __init__(self, blue_data):
        super().__init__()
        self.WT=[]
        self.blue_data:Queue = blue_data

    async def scan_for_devices(self):
        '''
        - introduce : 不断扫描蓝牙设备
        '''
        while True:
            try:
                devices = await BleakScanner.discover()
                filtered_devices = [device for device in devices if device.name!=None and device.name[:2]=="WT"]
                if filtered_devices:
                    for device in filtered_devices:
                        if device.name in self.WT:
                            continue
                        print(f"扫描到设备: {device.name}, 地址: {device.address}")
                        asyncio.create_task(self.main_1(device.address, device.name))                    
            except bleak.exc.BleakError as e:
                print(e)
                await asyncio.sleep(180)     
            finally:
                await asyncio.sleep(1)      

    async def main_1(self, address, name):
        def notification_handler(sender, data):
            axL = data[2]
            axH = data[3]
            a1 = axH << 8 | axL
            number = {'name':name}
            if a1 & 0x8000 == 0x8000:
                b = bin(a1)
                c = b[2:18]
                vv = [1 if i == '0' else 0 for i in c]
                s = ''.join(map(str, vv))
                aa = int(s, 2)
                c = ~aa
                x_a = c / 32768 * 16             
                current_time = datetime.now().strftime('%H:%M:%S')
                number['time'] = str(current_time)
                number['X'] = float(x_a)
            else:
                x_a = a1 / 32768 * 16
                current_time = datetime.now().strftime('%H:%M:%S')  
                number['time'] = str(current_time)
                number['X'] = float(x_a) 
            axL = data[4]
            axH = data[5]
            a1 = axH << 8 | axL
            if a1 & 0x8000 == 0x8000:
                b = bin(a1)
                c = b[2:18]
                vv = [1 if i == '0' else 0 for i in c]
                s = ''.join(map(str, vv))
                aa = int(s, 2)
                c = ~aa
                x_a = c / 32768 * 16

                number['Y'] = float(x_a)                                
            else:
                x_a = a1 / 32768 * 16

                number['Y'] = float(x_a)                     
            axL = data[6]
            axH = data[7]
            a1 = axH << 8 | axL
            if a1 & 0x8000 == 0x8000:
                b = bin(a1)
                c = b[2:18]
                vv = [1 if i == '0' else 0 for i in c]
                s = ''.join(map(str, vv))
                aa = int(s, 2)
                c = ~aa
                x_a = c / 32768 * 16
                number['Z'] = float(x_a)                               
            else:
                x_a = a1 / 32768 * 16
                number['Z'] = float(x_a)  
            number['blue_list']=self.WT
            self.write_data([number["time"],number['X'],number['Y'],number['Z']],name)
            try:
                self.blue_data.put(number, block=False)
            except queue.Full:
                pass
                # print("队列已满，无法放入数据")
            except Exception as e:
                print(f"发生错误：{e}")


        client = None
        try:
            client = BleakClient(address, timeout=3)
            await client.connect()
            print(f"{name}连接状态: {client.is_connected}")
            self.WT.append(name)
            await client.start_notify(14, notification_handler)
            while True:
                if not client.is_connected:
                    print(f"蓝牙{name}断开")   
                    self.WT.remove(name)
                    return         
                await asyncio.sleep(0.1)
        except Exception as e:
            print(f"{name}连接失败, {e}")             
            return
        
    def run(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        tasks = [self.scan_for_devices()]
        loop.run_until_complete(asyncio.wait(tasks))


    def write_data(self,data_list,name):    
        '''
        - introduce : 将数据写入CSV文本文件
        - param_1 : 数据列表
        - param_2 : 设备名称
        '''
        try:
            current_time = str(datetime.now().strftime('%Y-%m-%d'))
            directory = "data/WT_data"
            if not os.path.exists(directory):
                os.makedirs(directory)               
            file_path = os.path.join("data","WT_data", current_time + "-" + name + ".csv")#文件名称路径
            if not os.path.exists(file_path):#如果文件不存在
                with open(file_path, 'w', newline='',encoding='utf-8') as file:  
                    writer = csv.writer(file)  
                    # 写入CSV文件的列名  
                    writer.writerow(['Date_Time', 'X_Acceler', 'Y_Acceler','Z_Acceler'])  
            with open(file_path, 'a', newline='',encoding='UTF8') as file:  
                writer = csv.writer(file)  
                writer.writerow(data_list)     
        except Exception as e:
            print(e)



    import psutil
    import matplotlib.pyplot as plt
    import time

    # 监控当前进程的 CPU 和内存
    p = psutil.Process()
    cpu_percent = []
    mem_percent = []

    plt.ion()  # 开启交互模式
    fig, ax = plt.subplots()
    for _ in range(100):
        cpu_percent.append(p.cpu_percent())
        mem_percent.append(p.memory_percent())
        ax.clear()
        ax.plot(cpu_percent, label='CPU%')
        ax.plot(mem_percent, label='Memory%')
        ax.legend()
        plt.pause(0.1)
    

