# 核心模块
# 专业人员
# 绞车工
# 共享UI组件
# 动画系统
# 响应式布局
#
# 主题颜色，文件
# 主题文字
# 窗口定义


### 核心模块
# 数据采集
# 算法处理
# 实时监控
# 历史数据
# 设备管理
# 用户管理
# 系统管理

# 推荐选择：JSON文件存储 + PBKDF2加盐哈希 + 基础加密 的组合方案，兼顾安全性和实现复杂度。
# 用户配置文件存储在本地


通用模块：
工具类
图片资源类 
使用的qml 来对图片、文字、文本国际化
- 图片资源的命名规则，建议图片按照UI模块及大小来命名
- 程序界面的参数，包括各种UI颜色、大小等定义
其中AppThemeBase.qml定义了程序界面所有的资源及定位宽高信息


# 优先级
数据采集 > 算法处理 > 实时监控 > 历史数据 > 历史数据分析


// 项目总体框架


├── README
├── app.py                                      程序入口
├── config
│   ├── config.ini                              敏感信息 密码等的存储
│   └── globals.py                              公共变量
├── requirements.txt
└── src
    ├── common                                  公共组件
    │   ├── Log
    │   │   └── readme.md
    │   ├── Style
    │   │   ├── Audio
    │   │   ├── Font
    │   │   ├── readme.md
    │   │   ├── resources
    │   │   └── themes
    │   ├── __pycache__
    │   │   └── config_manager.cpython-39.pyc
    │   ├── config_manager.py
    │   ├── i18n
    │   │   ├── en_US.py
    │   │   ├── readme.md
    │   │   └── zh_CN.py
    │   └── readme.md
    ├── core                                    核心层
    │   ├── Algorithm
    │   │   └── readme.md
    │   ├── Device_connection
    │   │   ├── Bluetooth_sensor.py
    │   │   ├── wifi_acme.py
    │   │   └── winch_send.py
    │   ├── Historical_data
    │   │   ├── JC_data
    │   │   │   └── readme.md
    │   │   ├── PC_data
    │   │   │   └── readme.md
    │   │   ├── WT_data
    │   │   │   └── readme.md
    │   │   └── readme.md
    │   └── readme.md
    ├── professional_system                     专业人员子系统
    │   └── readme.md
    ├── shared_ui                   
    │   ├── CircularGauge.py                    公共ui组件
    ├── windows                                 公共窗口
    │   ├── login__window
    │   └── main_window
    └── worker_system                           绞车工子系统
        ├── equipment_management
        ├── readme.md
        └── real_time
            ├── RealTimeHander.py
            ├── appraltime.py
            ├── controller
            │   └── RealtimeController.py
            └── view
                └── RealTimeView.py