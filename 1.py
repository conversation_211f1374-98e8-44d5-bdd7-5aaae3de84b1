
class Parent:
    def __init__(self):
        self.__secret = 42  # 转换为 _Parent__secret

class Child(Parent):
    def __init__(self):
        super().__init__()
        self.__secret = 99  # 转换为 _Child__secret（不与父类的 __secret 冲突）

obj = Child()
print(obj.__dict__)  # 输出：{'_Parent__secret': 42, '_Child__secret': 99}

class Vector:
    def __init__(self, x, y):
        self.x = x
        self.y = y

    def __add__(self, other):  # 重载 + 运算符
        return Vector(self.x + other.x, self.y + other.y)

    def __str__(self):  # 定义对象的字符串表示
        return f"Vector({self.x}, {self.y})"
    
v1 = Vector(1, 2)
v2 = Vector(3, 4)
v3 = v1 + v2  # 调用 __add__ 方法
print(v3)  # 输出: Vector(4, 6)