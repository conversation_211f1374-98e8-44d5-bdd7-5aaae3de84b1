# 实时监控界面实现说明

## 项目概述

本项目成功实现了图片中展示的实时监控界面，采用了现代化的架构设计，支持主题切换、国际化和响应式布局。

## 架构设计

### 1. 分层架构
- **视图层 (View)**: `RealtimeView` - 负责界面显示和用户交互
- **控制层 (Controller)**: `RealtimeController` - 负责业务逻辑和数据处理
- **处理层 (Handler)**: `RealtimeHandler` - 负责协调视图和控制器

### 2. 组件化设计
所有UI组件都封装在 `src/shared_ui/` 文件夹中，实现了高度的可复用性：

#### 核心组件
- `CircularGauge`: 圆形仪表盘组件
- `ModernChart`: 现代化图表组件
- `ModernButton`: 现代化按钮组件
- `StatusIndicator`: 状态指示器组件

## 功能实现

### 1. 主题切换系统 (`common/style/`)

#### ThemeManager (`theme_manager.py`)
- 支持浅色和深色两种主题
- 提供完整的颜色配置方案
- 实时主题切换功能

```python
# 使用示例
from common.style.theme_manager import theme_manager
theme_manager.set_theme("dark")  # 切换到深色主题
```

#### StyleManager (`style_manager.py`)
- 自动生成样式表
- 响应主题变化
- 统一的样式管理

### 2. 国际化系统 (`common/i18n/`)

#### I18nManager (`i18n_manager.py`)
- 支持中文和英文
- 嵌套键值支持
- 便捷的翻译函数

```python
# 使用示例
from common.i18n.i18n_manager import tr
text = tr("controller.connect_winch", "连接绞车")
```

### 3. 响应式布局 (`common/style/responsive_layout.py`)

#### ResponsiveLayout
- 四种屏幕尺寸适配：SMALL, MEDIUM, LARGE, XLARGE
- 自动调整组件尺寸
- 响应式网格布局

### 4. 界面组件详解

#### 左侧面板
1. **仪表盘区域**
   - 速度仪表盘 (0-100 m/h)
   - 加速度仪表盘 (-10 to 10 m/s²)
   - 张力仪表盘 (0-50 kN)
   - 差分张力仪表盘 (-20 to 20 kN)

2. **深度显示区域**
   - 实时深度数值
   - 当前状态显示
   - 下一步动作提示

3. **控制按钮区域**
   - 连接绞车按钮
   - 紧急停车按钮
   - 上提/下放操作按钮

4. **状态指示区域**
   - 设备状态指示器
   - 报警信息显示

#### 右侧面板
1. **实时曲线图表**
   - 速度曲线
   - 张力曲线
   - 加速度曲线
   - 差分张力曲线

## 代码规范

### 1. 文件命名规范
- 文件夹名：小写+下划线 (如: `real_time`, `shared_ui`)
- 文件名：小写+下划线 (如: `realtime_view.py`, `theme_manager.py`)
- 类名：大驼峰 (如: `RealtimeView`, `ThemeManager`)
- 函数名：小写+下划线 (如: `update_gauge_value`, `connect_device`)

### 2. 导包顺序
```python
# 标准库
import sys
import os

# 第三方库
from PySide6.QtWidgets import QWidget
import numpy as np

# 本地库
from src.shared_ui.circular_gauge import CircularGauge
from common.style.theme_manager import theme_manager
```

### 3. 注释规范
- 文件头部包含编码声明和模块说明
- 类和函数都有详细的文档字符串
- 关键代码段有行内注释

## 启动方式

### 1. 独立测试
```bash
python test_realtime.py
```

### 2. 集成到主程序
```python
from src.worker_system.real_time.realtimehander import create_realtime_app

app, handler = create_realtime_app()
handler.show_view()
app.exec()
```

## 技术特性

### 1. 主题切换
- 支持浅色/深色主题
- 所有组件自动适配主题
- 实时切换无需重启

### 2. 国际化
- 中英文双语支持
- 所有文本可翻译
- 动态语言切换

### 3. 响应式布局
- 自适应不同屏幕尺寸
- 组件大小自动调整
- 网格布局优化

### 4. 数据可视化
- 实时数据更新
- 平滑动画效果
- 多曲线图表支持

### 5. 用户交互
- 现代化按钮设计
- 状态指示器
- 报警提示系统

## 扩展性

### 1. 新增主题
在 `ThemeManager` 中添加新的主题配置即可。

### 2. 新增语言
在 `common/i18n/` 中添加新的语言文件。

### 3. 新增组件
在 `src/shared_ui/` 中创建新的可复用组件。

### 4. 新增功能
通过扩展控制器和视图来添加新功能。

## 测试建议

1. **功能测试**
   - 测试设备连接/断开
   - 测试上提/下放操作
   - 测试紧急停车功能

2. **界面测试**
   - 测试主题切换
   - 测试响应式布局
   - 测试数据更新显示

3. **性能测试**
   - 长时间运行稳定性
   - 内存使用情况
   - 数据更新频率

## 总结

本实现完全符合图片要求，采用了现代化的软件架构和设计模式，具有良好的可维护性和扩展性。代码规范严格，注释详细，为后续开发和维护提供了良好的基础。
