# -*- coding: utf-8 -*-
"""
样式管理器
负责生成和管理应用程序的样式表
"""

# 标准库
from typing import Dict, Any

# 第三方库
from PySide6.QtCore import QObject, Signal

# 本地库
from common.style.theme_manager import theme_manager
from common.style.responsive_layout import responsive_layout


class StyleManager(QObject):
    """样式管理器"""
    
    # 样式更新信号
    style_updated = Signal()
    
    def __init__(self):
        super().__init__()
        # 连接主题和布局变化信号
        theme_manager.theme_changed.connect(self._on_theme_changed)
        responsive_layout.screen_size_changed.connect(self._on_screen_size_changed)
    
    def _on_theme_changed(self, theme_name: str):
        """主题变化处理"""
        self.style_updated.emit()
    
    def _on_screen_size_changed(self, screen_size):
        """屏幕尺寸变化处理"""
        self.style_updated.emit()
    
    def get_main_window_style(self) -> str:
        """获取主窗口样式"""
        colors = theme_manager.get_colors()
        config = responsive_layout.get_all_config()
        
        return f"""
        QMainWindow {{
            background-color: {colors['MAIN_BG']};
            color: {colors['TEXT']};
            font-size: {config['font_size_normal']}px;
        }}
        
        QWidget {{
            background-color: {colors['MAIN_BG']};
            color: {colors['TEXT']};
        }}
        """
    
    def get_button_style(self, button_type: str = "primary") -> str:
        """获取按钮样式"""
        colors = theme_manager.get_colors()
        height = responsive_layout.get_button_height()
        font_size = responsive_layout.get_font_size("normal")
        
        button_colors = {
            "primary": (colors['BUTTON_PRIMARY'], colors['BUTTON_PRIMARY_HOVER']),
            "secondary": (colors['BUTTON_SECONDARY'], colors['BUTTON_SECONDARY_HOVER']),
            "success": (colors['BUTTON_SUCCESS'], colors['BUTTON_SUCCESS_HOVER']),
            "warning": (colors['BUTTON_WARNING'], colors['BUTTON_WARNING_HOVER']),
            "danger": (colors['BUTTON_DANGER'], colors['BUTTON_DANGER_HOVER'])
        }
        
        bg_color, hover_color = button_colors.get(button_type, button_colors["primary"])
        
        return f"""
        QPushButton {{
            background-color: {bg_color};
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: {font_size}px;
            font-weight: bold;
            min-height: {height}px;
        }}
        
        QPushButton:hover {{
            background-color: {hover_color};
        }}
        
        QPushButton:pressed {{
            background-color: {hover_color};
            margin-top: 1px;
        }}
        
        QPushButton:disabled {{
            background-color: {colors['TEXT_DISABLED']};
            color: {colors['TEXT_SECONDARY']};
        }}
        """
    
    def get_card_style(self) -> str:
        """获取卡片样式"""
        colors = theme_manager.get_colors()
        margin = responsive_layout.get_margin()

        return f"""
        QFrame[cardStyle="true"] {{
            background-color: {colors['CARD_BG']};
            border: 1px solid {colors['BORDER']};
            border-radius: 8px;
            margin: {margin}px;
            padding: {margin}px;
        }}

        /* 深度显示框 - 蓝色背景 */
        QFrame[cardStyle="depth"] {{
            background-color: {colors['PRIMARY_LIGHT']};
            border: 2px solid {colors['PRIMARY']};
            border-radius: 12px;
            margin: {margin//2}px;
            padding: {margin}px;
            min-height: 60px;
        }}

        /* 状态显示框 - 绿色背景 */
        QFrame[cardStyle="status"] {{
            background-color: rgba(76, 175, 80, 0.2);
            border: 2px solid {colors['SUCCESS']};
            border-radius: 12px;
            margin: {margin//2}px;
            padding: {margin}px;
            min-height: 60px;
        }}

        /* 动作显示框 - 橙色背景 */
        QFrame[cardStyle="action"] {{
            background-color: rgba(255, 152, 0, 0.2);
            border: 2px solid {colors['WARNING']};
            border-radius: 12px;
            margin: {margin//2}px;
            padding: {margin}px;
            min-height: 60px;
        }}
        """
    
    def get_gauge_style(self) -> str:
        """获取仪表盘样式"""
        colors = theme_manager.get_colors()
        size = responsive_layout.get_gauge_size()
        
        return f"""
        QWidget[gaugeStyle="true"] {{
            background-color: {colors['GAUGE_BG']};
            border: 2px solid {colors['GAUGE_BORDER']};
            border-radius: {size//2}px;
            min-width: {size}px;
            min-height: {size}px;
            max-width: {size}px;
            max-height: {size}px;
        }}
        """
    
    def get_chart_style(self) -> str:
        """获取图表样式"""
        colors = theme_manager.get_colors()
        height = responsive_layout.get_chart_height()
        
        return f"""
        QWidget[chartStyle="true"] {{
            background-color: {colors['CARD_BG']};
            border: 1px solid {colors['BORDER']};
            border-radius: 8px;
            min-height: {height}px;
        }}
        """
    
    def get_sidebar_style(self, expanded: bool = False) -> str:
        """获取侧边栏样式"""
        colors = theme_manager.get_colors()
        width = responsive_layout.get_sidebar_width(expanded)
        
        return f"""
        QWidget[sidebarStyle="true"] {{
            background-color: {colors['SIDEBAR_BG']};
            border-right: 1px solid {colors['BORDER']};
            min-width: {width}px;
            max-width: {width}px;
        }}
        """
    
    def get_label_style(self, label_type: str = "normal") -> str:
        """获取标签样式"""
        colors = theme_manager.get_colors()
        font_sizes = {
            "small": responsive_layout.get_font_size("small"),
            "normal": responsive_layout.get_font_size("normal"),
            "large": responsive_layout.get_font_size("large")
        }
        
        font_size = font_sizes.get(label_type, font_sizes["normal"])
        
        return f"""
        QLabel[labelType="{label_type}"] {{
            color: {colors['TEXT']};
            font-size: {font_size}px;
        }}
        
        QLabel[labelType="secondary"] {{
            color: {colors['TEXT_SECONDARY']};
        }}
        
        QLabel[labelType="title"] {{
            background-color: transparent;
            color: {colors['TEXT']};
            font-size: {font_sizes['large']}px;
            font-weight: bold;
        }}

        QLabel[labelType="large"] {{
            background-color: transparent;
            color: {colors['TEXT']};
            font-size: {font_sizes['large']}px;
            font-weight: bold;
        }}

        QLabel[labelType="value"] {{
            background-color: transparent;
            color: {colors['PRIMARY']};
            font-size: {font_sizes['large']}px;
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            background: rgba(33, 150, 243, 0.1);
        }}

        QLabel[labelType="scale"] {{
            background-color: transparent;
            color: {colors['TEXT_SECONDARY']};
            font-size: {font_sizes['small']}px;
        }}
        """

        
    
    def get_input_style(self) -> str:
        """获取输入框样式"""
        colors = theme_manager.get_colors()
        height = responsive_layout.get_button_height()
        
        return f"""
        QLineEdit, QTextEdit, QComboBox {{
            background-color: {colors['CARD_BG']};
            border: 1px solid {colors['BORDER']};
            border-radius: 4px;
            padding: 8px;
            color: {colors['TEXT']};
            min-height: {height}px;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
            border-color: {colors['BORDER_FOCUS']};
        }}
        """
    
    def get_complete_style(self) -> str:
        """获取完整样式表"""
        return f"""
        {self.get_main_window_style()}
        {self.get_button_style("primary")}
        {self.get_card_style()}
        {self.get_gauge_style()}
        {self.get_chart_style()}
        {self.get_label_style()}
        {self.get_input_style()}
        """


# 全局样式管理器实例
style_manager = StyleManager()
