# -*- coding: utf-8 -*-
"""
主题管理器
负责管理应用程序的主题切换、颜色配置等
"""

# 标准库
import os
import json
from typing import Dict, Any

# 第三方库
from PySide6.QtCore import QObject, Signal
from PySide6.QtGui import QColor

# 本地库
# from config.globals import CONFIG_PATH  # 暂时注释掉，使用相对路径


class ThemeManager(QObject):
    """主题管理器"""
    
    # 主题变更信号
    theme_changed = Signal(str)  # 主题名称
    
    def __init__(self):
        super().__init__()
        self._current_theme = "light"
        self._themes = {}
        self._load_themes()
    
    def _load_themes(self):
        """加载主题配置"""
        # 浅色主题
        self._themes["light"] = {
            "name": "浅色主题",
            "colors": {
                # 主要颜色
                "PRIMARY": "#2196F3",
                "PRIMARY_DARK": "#1976D2", 
                "PRIMARY_LIGHT": "#BBDEFB",
                
                # 背景颜色
                "MAIN_BG": "#FFFFFF",
                "SECONDARY_BG": "#F5F5F5",
                "CARD_BG": "#FFFFFF",
                "SIDEBAR_BG": "#FAFAFA",
                
                # 文字颜色
                "TEXT": "#212121",
                "TEXT_SECONDARY": "#757575",
                "TEXT_DISABLED": "#BDBDBD",
                
                # 边框颜色
                "BORDER": "#E0E0E0",
                "BORDER_FOCUS": "#2196F3",
                
                # 状态颜色
                "SUCCESS": "#4CAF50",
                "WARNING": "#FF9800",
                "ERROR": "#F44336",
                "INFO": "#2196F3",
                
                # 按钮颜色
                "BUTTON_PRIMARY": "#2196F3",
                "BUTTON_PRIMARY_HOVER": "#1976D2",
                "BUTTON_SECONDARY": "#757575",
                "BUTTON_SECONDARY_HOVER": "#616161",
                "BUTTON_SUCCESS": "#4CAF50",
                "BUTTON_SUCCESS_HOVER": "#388E3C",
                "BUTTON_WARNING": "#FF9800",
                "BUTTON_WARNING_HOVER": "#F57C00",
                "BUTTON_DANGER": "#F44336",
                "BUTTON_DANGER_HOVER": "#D32F2F",
                
                # 图表颜色
                "CHART_LINE_1": "#2196F3",
                "CHART_LINE_2": "#4CAF50", 
                "CHART_LINE_3": "#FF9800",
                "CHART_LINE_4": "#F44336",
                "CHART_GRID": "#E0E0E0",
                "CHART_AXIS": "#757575",
                
                # 仪表盘颜色
                "GAUGE_BG": "#F5F5F5",
                "GAUGE_BORDER": "#E0E0E0",
                "GAUGE_NEEDLE": "#F44336",
                "GAUGE_TEXT": "#212121"
            }
        }
        
        # 深色主题
        self._themes["dark"] = {
            "name": "深色主题",
            "colors": {
                # 主要颜色
                "PRIMARY": "#64B5F6",
                "PRIMARY_DARK": "#42A5F5",
                "PRIMARY_LIGHT": "#90CAF9",
                
                # 背景颜色
                "MAIN_BG": "#121212",
                "SECONDARY_BG": "#1E1E1E",
                "CARD_BG": "#2D2D2D",
                "SIDEBAR_BG": "#1A1A1A",
                
                # 文字颜色
                "TEXT": "#FFFFFF",
                "TEXT_SECONDARY": "#B0B0B0",
                "TEXT_DISABLED": "#666666",
                
                # 边框颜色
                "BORDER": "#404040",
                "BORDER_FOCUS": "#64B5F6",
                
                # 状态颜色
                "SUCCESS": "#66BB6A",
                "WARNING": "#FFB74D",
                "ERROR": "#EF5350",
                "INFO": "#64B5F6",
                
                # 按钮颜色
                "BUTTON_PRIMARY": "#64B5F6",
                "BUTTON_PRIMARY_HOVER": "#42A5F5",
                "BUTTON_SECONDARY": "#757575",
                "BUTTON_SECONDARY_HOVER": "#616161",
                "BUTTON_SUCCESS": "#66BB6A",
                "BUTTON_SUCCESS_HOVER": "#4CAF50",
                "BUTTON_WARNING": "#FFB74D",
                "BUTTON_WARNING_HOVER": "#FF9800",
                "BUTTON_DANGER": "#EF5350",
                "BUTTON_DANGER_HOVER": "#F44336",
                
                # 图表颜色
                "CHART_LINE_1": "#64B5F6",
                "CHART_LINE_2": "#66BB6A",
                "CHART_LINE_3": "#FFB74D", 
                "CHART_LINE_4": "#EF5350",
                "CHART_GRID": "#404040",
                "CHART_AXIS": "#B0B0B0",
                
                # 仪表盘颜色
                "GAUGE_BG": "#2D2D2D",
                "GAUGE_BORDER": "#404040",
                "GAUGE_NEEDLE": "#EF5350",
                "GAUGE_TEXT": "#FFFFFF"
            }
        }
    
    def get_current_theme(self) -> str:
        """获取当前主题名称"""
        return self._current_theme
    
    def set_theme(self, theme_name: str):
        """设置主题"""
        if theme_name in self._themes:
            self._current_theme = theme_name
            self.theme_changed.emit(theme_name)
    
    def get_color(self, color_key: str) -> str:
        """获取颜色值"""
        theme = self._themes.get(self._current_theme, self._themes["light"])
        return theme["colors"].get(color_key, "#000000")
    
    def get_colors(self) -> Dict[str, str]:
        """获取当前主题的所有颜色"""
        theme = self._themes.get(self._current_theme, self._themes["light"])
        return theme["colors"]
    
    def get_available_themes(self) -> Dict[str, str]:
        """获取可用主题列表"""
        return {name: theme["name"] for name, theme in self._themes.items()}


# 全局主题管理器实例
theme_manager = ThemeManager()
