# -*- coding: utf-8 -*-
"""
响应式布局管理器
负责管理界面的响应式布局和自适应调整
"""

# 标准库
from typing import Dict, Tu<PERSON>, List
from enum import Enum

# 第三方库
from PySide6.QtCore import QObject, Signal, QSize
from PySide6.QtWidgets import QWidget


class ScreenSize(Enum):
    """屏幕尺寸枚举"""
    SMALL = "small"      # < 1024px
    MEDIUM = "medium"    # 1024px - 1440px
    LARGE = "large"      # 1440px - 1920px
    XLARGE = "xlarge"    # > 1920px


class ResponsiveLayout(QObject):
    """响应式布局管理器"""
    
    # 屏幕尺寸变化信号
    screen_size_changed = Signal(ScreenSize)
    
    def __init__(self):
        super().__init__()
        self._current_size = ScreenSize.MEDIUM
        self._breakpoints = {
            ScreenSize.SMALL: 1024,
            ScreenSize.MEDIUM: 1440,
            ScreenSize.LARGE: 1920
        }
        
        # 响应式配置
        self._configs = {
            ScreenSize.SMALL: {
                "sidebar_width": 60,
                "sidebar_expanded_width": 200,
                "gauge_size": 180,
                "chart_height": 200,
                "button_height": 32,
                "font_size_small": 10,
                "font_size_normal": 12,
                "font_size_large": 14,
                "spacing": 8,
                "margin": 12,
                "grid_columns": 2
            },
            ScreenSize.MEDIUM: {
                "sidebar_width": 80,
                "sidebar_expanded_width": 250,
                "gauge_size": 220,
                "chart_height": 250,
                "button_height": 36,
                "font_size_small": 11,
                "font_size_normal": 13,
                "font_size_large": 16,
                "spacing": 10,
                "margin": 16,
                "grid_columns": 3
            },
            ScreenSize.LARGE: {
                "sidebar_width": 100,
                "sidebar_expanded_width": 300,
                "gauge_size": 250,
                "chart_height": 300,
                "button_height": 40,
                "font_size_small": 12,
                "font_size_normal": 14,
                "font_size_large": 18,
                "spacing": 12,
                "margin": 20,
                "grid_columns": 4
            },
            ScreenSize.XLARGE: {
                "sidebar_width": 120,
                "sidebar_expanded_width": 350,
                "gauge_size": 280,
                "chart_height": 350,
                "button_height": 44,
                "font_size_small": 13,
                "font_size_normal": 15,
                "font_size_large": 20,
                "spacing": 16,
                "margin": 24,
                "grid_columns": 5
            }
        }
    
    def update_screen_size(self, width: int, height: int):
        """更新屏幕尺寸"""
        new_size = self._calculate_screen_size(width)
        if new_size != self._current_size:
            self._current_size = new_size
            self.screen_size_changed.emit(new_size)
    
    def _calculate_screen_size(self, width: int) -> ScreenSize:
        """根据宽度计算屏幕尺寸"""
        if width < self._breakpoints[ScreenSize.SMALL]:
            return ScreenSize.SMALL
        elif width < self._breakpoints[ScreenSize.MEDIUM]:
            return ScreenSize.MEDIUM
        elif width < self._breakpoints[ScreenSize.LARGE]:
            return ScreenSize.LARGE
        else:
            return ScreenSize.XLARGE
    
    def get_current_size(self) -> ScreenSize:
        """获取当前屏幕尺寸"""
        return self._current_size
    
    def get_config(self, key: str) -> int:
        """获取当前屏幕尺寸的配置值"""
        config = self._configs.get(self._current_size, self._configs[ScreenSize.MEDIUM])
        return config.get(key, 0)
    
    def get_all_config(self) -> Dict[str, int]:
        """获取当前屏幕尺寸的所有配置"""
        return self._configs.get(self._current_size, self._configs[ScreenSize.MEDIUM])
    
    def get_gauge_size(self) -> int:
        """获取仪表盘尺寸"""
        return self.get_config("gauge_size")
    
    def get_chart_height(self) -> int:
        """获取图表高度"""
        return self.get_config("chart_height")
    
    def get_sidebar_width(self, expanded: bool = False) -> int:
        """获取侧边栏宽度"""
        if expanded:
            return self.get_config("sidebar_expanded_width")
        return self.get_config("sidebar_width")
    
    def get_button_height(self) -> int:
        """获取按钮高度"""
        return self.get_config("button_height")
    
    def get_font_size(self, size_type: str = "normal") -> int:
        """获取字体大小"""
        return self.get_config(f"font_size_{size_type}")
    
    def get_spacing(self) -> int:
        """获取间距"""
        return self.get_config("spacing")
    
    def get_margin(self) -> int:
        """获取边距"""
        return self.get_config("margin")
    
    def get_grid_columns(self) -> int:
        """获取网格列数"""
        return self.get_config("grid_columns")
    
    def is_mobile_size(self) -> bool:
        """是否为移动端尺寸"""
        return self._current_size == ScreenSize.SMALL
    
    def is_desktop_size(self) -> bool:
        """是否为桌面端尺寸"""
        return self._current_size in [ScreenSize.LARGE, ScreenSize.XLARGE]


# 全局响应式布局管理器实例
responsive_layout = ResponsiveLayout()
