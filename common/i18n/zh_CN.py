TEXTS = {
    "window_title": "测井仪器井下运行状态智能诊断系统",
    "device_tab": "设备",
    "operator_tab": "操作员",
    "connect_btn": "连接",
    "disconnect_btn": "断开",
    "status_label": "状态:",
    "settings_btn": "设置",
    
    # 导航栏文本
    "real_time_monitor": "实时",
    "schedule_management": "排程",
    "device_management": "设备",
    "system_settings": "设置",
    
    # 设置相关文本
    "settings_title": "系统设置",
    "language_settings": "语言选择",
    "theme_settings": "主题设置",
    "chinese": "中文",
    "english": "English",
    "light_theme": "浅色主题",
    "dark_theme": "深色主题",
    
    # 按钮文本
    "pin_sidebar": "锁定/解锁 侧边栏",
    "expand_sidebar": "展开/收起 侧边栏",
    "minimize": "最小化",
    "maximize": "最大化",
    "restore": "还原",
    "close": "关闭",
    
    # 导出相关文本
    "export_success": "导出成功",
    "export_path": "报警记录已成功导出到：",
    "confirm": "确定",
    "cancel": "取消",
    
    # 清空记录相关文本
    "clear_confirm_title": "确认清空",
    "clear_confirm_msg": "确定要清空所有报警记录吗？",
    
    # 状态文本
    "device_not_connected": "设备未连接",
    "waiting_start": "等待启动",
    "normal_operation": "正常运行",
    
    # 设备状态相关文本
    "current_device": "当前设备",
    "device_status": "设备状态",
    "normal_running": "正常运行",
    
    # 系统名称相关
    "system_name": "测井仪器井下运行状态智能诊断系统",
    
    # 标题栏按钮符号
    "minimize_symbol": "—",
    "maximize_symbol": "□",
    "close_symbol": "×",
    "menu_symbol": "≡",
    
    # 设置弹窗相关
    "settings_dialog_title": "系统设置",
    "settings_language": "语言选择",
    "settings_theme": "主题设置",
    "settings_zh": "🇨🇳 中文",
    "settings_en": "🇺🇸 English",
    "settings_light": "☀️ 浅色主题",
    "settings_dark": "🌙 深色主题",
    
    # 导航栏按钮
    "nav_monitor": "实时",
    "nav_schedule": "排程",
    "nav_device": "设备",
    
    # 窗口控制按钮
    "window_minimize": "—",
    "window_maximize": "□",
    "window_restore": "❐",
    "window_close": "×",
    
    # 其他界面元素
    "loading": "加载中...",
    "error": "错误",
    "warning": "警告",
    "info": "信息",
    "success": "成功",
    
    # 操作员视图相关文本
    "operator_title": "测井仪器井下运行状态智能诊断系统 - 绞车工工作台",
    
    # 仪表盘标题
    "speed_gauge": "速度",
    "acceleration_gauge": "加速度",
    "tension_gauge": "张力",
    "diff_tension_gauge": "差分张力",
    
    # 单位
    "speed_unit": " m/h",
    "acceleration_unit": " m/s²",
    "tension_unit": " kN",
    "depth_unit": " m",
    
    # 状态显示
    "depth_title": "深度",
    "current_status": "实时状态",
    "next_action": "下一步动作",
    
    # 报警相关
    "alarm_warning": "⚠",
    "alarm_obstacle": "设备遇阻",
    "expand_history": "展开历史",
    
    # 控制按钮
    "connect_winch": "连接绞车",
    "emergency_stop": "紧急停车",
    "lift_up": "上提",
    "lower_down": "下放",
    "increase_torque": "增大扭矩",
    "decrease_torque": "减小扭矩",
    
    # 图表标题
    "speed_curve": "速度曲线",
    "tension_curve": "张力曲线",
    "acceleration_curve": "加速度曲线",
    "diff_tension_curve": "差分张力曲线",
    
    # 操作员视图补充文本
    "time_axis": "时间",
    "speed_value": "速度值",
    "torque_value": "扭矩值",
    "refresh_params": "参数已刷新！",
    "save_config": "配置已保存！",
    "speed_control": "速度控制",
    "torque_control": "扭矩控制",
    "speed_value_format": "{:.1f} m/h",
    "torque_value_format": "{:.1f} N·m",
    
    # 状态更新相关
    "error_update": "更新显示时出错: {}",
    
    # 报警时间和深度
    "alarm_time": "03:49:10",
    "alarm_depth": "2427m",
    
    # 图表相关
    "plot_styles": {
        "speed": "速度曲线",
        "tension": "张力曲线",
        "acceleration": "加速度曲线",
        "diff_tension": "差分张力曲线"
    },
    
    # 报警相关补充
    "alarm_types": {
        "device_stuck": "设备遇卡",
        "tension_abnormal": "张力异常",
        "speed_fluctuation": "速度波动"
    },
    "alarm_icons": {
        "warning": "⚠",
        "stuck": "⛔",
        "tension": "⚡",
        "speed": "📊"
    },
    "export_records": "导出记录",
    
    # 状态和控制相关补充
    "status_display": {
        "normal": "正常运行",
        "warning": "警告状态",
        "error": "错误状态",
        "offline": "离线状态"
    },
    
    # 图表和数据相关补充
    "chart_axis": {
        "time": "时间",
        "speed": "速度",
        "tension": "张力",
        "acceleration": "加速度"
    },
    
    # 操作控制器相关文本
    "controller": {
        # 按钮状态
        "connect_winch": "连接绞车",
        "start": "启动",
        "disconnect_winch": "断开绞车",
        "lift_up": "上提",
        "stop_lift": "停止上提",
        "lower_down": "下放",
        "stop_lower": "停止下放",
        
        # 设备状态
        "device_running": "设备运行中",
        "device_stopped": "设备停止",
        "next_lower": "设备下放",
        
        # 操作提示
        "operation_tip": "操作提示",
        "stop_lift_msg": "设备已停止上提",
        "stop_lower_msg": "设备已停止下放",
        
        # 数据源配置
        "data_source": {
            "acme_device": "ACME设备",
            "csv_file": "CSV文件",
            "network_conn": "网络连接",
            "depth_curve": "深度曲线名称",
            "speed_curve": "速度曲线名称",
            "tension_curve": "张力曲线名称",
            "csv_config": "CSV 文件配置",
            "file_path": "CSV文件路径",
            "browse": "浏览...",
            "depth_col": "深度列索引",
            "speed_col": "速度列索引",
            "tension_col": "张力列索引",
            "network_config": "网络配置",
            "data_format": "数据格式",
            "connect": "连接",
            "cancel": "取消"
        },
        
        # 错误和警告
        "load_schedule_error": "加载排程文件失败: {}",
        "schedule_load_failed": "排程数据加载失败",
        "command_failed": "指令发送失败",
        "check_connection": "与设备的通信失败，请检查连接",
        
        # 按钮样式
        "button_styles": {
            "normal": {
                "bg_color": "#28a745",
                "hover_color": "#218838"
            },
            "warning": {
                "bg_color": "#dc3545",
                "hover_color": "#c82333"
            },
            "disabled": {
                "bg_color": "#cccccc",
                "text_color": "#666666"
            }
        }
    },
    
    # 排程视图相关文本
    "schedule": {
        # 标题和标签
        "schedule_file_list": "排程文件列表",
        "jc_data_files": "JC数据文件",
        "search_label": "搜索文件:",
        "search_placeholder": "输入关键字搜索...",
        "current_file": "当前文件: {} ({}行 x {}列)",
        
        # 按钮文本
        "upload_schedule": "上传排程文件",
        "delete_file": "删除文件",
        "export_file": "导出文件",
        "refresh_data": "刷新数据",
        "save_changes": "保存修改",
        "download_selected": "下载选中",
        "delete_selected": "删除选中",
        
        # 表格列标题
        "table_headers": {
            "run_to": "运行到",
            "ref_depth": "参考深度",
            "run_speed": "运行速度",
            "record": "记录",
            "next_action": "到达后动作",
            "run_status": "运行状态"
        },
        
        # 状态文本
        "status": {
            "normal": "正常",
            "warning": "警告",
            "error": "错误",
            "no": "否",
            "yes": "是",
            "lower": "下放",
            "lift": "上升",
            "stop": "停止"
        },
        
        # 错误和提示消息
        "messages": {
            "display_error": "无法显示数据: {}",
            "delete_confirm": "确认删除",
            "delete_confirm_msg": "确定要删除选中的文件吗？",
            "delete_success": "删除成功",
            "delete_error": "删除失败: {}",
            "save_success": "保存成功",
            "save_error": "保存失败: {}",
            "no_file_selected": "请先选择文件",
            "download_success": "下载成功",
            "download_error": "下载失败: {}"
        },
        
        # 文件相关
        "file": {
            "page_info": "第 {} 页 / 共 {} 页",
            "page_error": "页码错误",
            "page_range_error": "请输入1至{}之间的页码",
            "invalid_page": "请输入有效数字",
            "upload_title": "上传文件",
            "upload_status": "正在连接到服务器...",
            "upload_ready": "连接成功，准备上传...",
            "upload_progress": "正在上传: {}% ({}/{} 字节)",
            "upload_complete": "上传完成！",
            "upload_success": "文件 {} 已成功上传到服务器",
            "upload_cancelled": "上传已取消",
            "upload_error": "上传错误: {}",
            "upload_failed": "上传失败: {}"
        },
        
        # 右键菜单
        "context_menu": {
            "toggle_select": "切换选中状态",
            "view_content": "查看文件内容",
            "select_file": "选中文件",
            "deselect_file": "取消选中"
        },
        
        # 错误提示
        "errors": {
            "display_error": "显示错误：{}",
            "upload_error": "上传错误：{}"
        }
    },
    
    # 设备视图相关文本
    "device": {
        # 标题和标签
        "device_info": "设备信息",
        "device_list": "设备列表",
        "device_status": "设备状态",
        "device_params": "设备参数",
        "device_control": "设备控制",
        
        # 设备信息标签
        "device_id": "设备ID",
        "device_name": "设备名称",
        "device_type": "设备类型",
        "device_model": "设备型号",
        "serial_number": "序列号",
        "manufacture_date": "生产日期",
        "last_maintenance": "上次维护",
        "next_maintenance": "下次维护",
        
        # 状态信息
        "status": {
            "online": "在线",
            "offline": "离线",
            "running": "运行中",
            "stopped": "已停止",
            "error": "错误",
            "maintenance": "维护中",
            "normal": "正常运行"
        },
        
        # 参数设置
        "params": {
            "speed_limit": "速度限制",
            "tension_limit": "张力限制",
            "acceleration_limit": "加速度限制",
            "max_depth": "最大深度",
            "unit_speed": "m/h",
            "unit_tension": "kN",
            "unit_acceleration": "m/s²",
            "unit_depth": "m"
        },
        
        # 按钮文本
        "buttons": {
            "add_device": "添加设备",
            "remove_device": "移除设备",
            "edit_device": "编辑设备",
            "save_params": "保存参数",
            "reset_params": "重置参数",
            "start_maintenance": "开始维护",
            "end_maintenance": "结束维护",
            "refresh": "刷新"
        },
        
        # 对话框文本
        "dialogs": {
            "add_title": "添加新设备",
            "edit_title": "编辑设备信息",
            "remove_title": "移除设备",
            "remove_confirm": "确定要移除该设备吗？",
            "save_success": "保存成功",
            "save_error": "保存失败: {}",
            "connection_error": "连接错误: {}"
        },
        
        # 提示信息
        "tooltips": {
            "speed_limit": "设置设备运行的最大速度",
            "tension_limit": "设置设备允许的最大张力",
            "acceleration_limit": "设置设备的最大加速度",
            "max_depth": "设置设备的最大工作深度"
        },
        
        # 错误消息
        "errors": {
            "load_failed": "加载设备信息失败: {}",
            "save_failed": "保存设备信息失败: {}",
            "connect_failed": "设备连接失败: {}",
            "invalid_param": "无效的参数值: {}"
        },
        
        # 表格标题
        "table_headers": {
            "param": ["参数名称", "参数值", "单位"],
            "maintenance": ["维护项目", "上次维护时间", "维护周期", "下次维护时间"],
            "well": ["井号", "井深(m)", "状态", "最近作业时间", "备注"],
            "stats": ["状态类型", "井号数量", "占比"]
        },
        
        # 分组标题
        "groups": {
            "basic_params": "基本参数",
            "maintenance_params": "维护参数",
            "well_info": "井号信息",
            "well_stats": "井号统计"
        },
        
        # 标签页标题
        "tabs": {
            "basic": "基本参数",
            "maintenance": "维护信息",
            "well_list": "井号列表",
            "stats": "统计信息"
        },
        
        # 井号相关按钮
        "well_buttons": {
            "add": "添加井号",
            "edit": "编辑井号",
            "delete": "删除井号"
        },
        
        # 状态文本
        "status_text": {
            "normal": "正常",
            "maintenance": "维护中",
            "repair": "检修"
        },
        
        # 样式配置
        "styles": {
            "colors": {
                "normal": "#4caf50",
                "warning": "#ff9800",
                "info": "#2196f3",
                "error": "#f44336"
            },
            "status_indicator": {
                "normal": "background-color: #4caf50; color: white; padding: 3px 8px; border-radius: 4px;"
            }
        }
    }
} 