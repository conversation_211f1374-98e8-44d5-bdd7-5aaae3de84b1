TEXTS = {
    # 主窗口相关
    "system_name": "Underground Monitoring System",
    "title_monitor": "Monitor",
    "title_schedule": "Schedule",
    "title_device": "Device",
    "settings": "Settings",
    "settings_language": "Language",
    "settings_theme": "Theme",
    "settings_zh": "Chinese",
    "settings_en": "English",
    "settings_light": "Light",
    "settings_dark": "Dark",
    "settings_dialog_title": "Settings",
    
    # 监控页面相关
    "monitor_title": "Real-time Monitor",
    "depth_label": "Current Depth",
    "speed_label": "Current Speed",
    "tension_label": "Current Tension",
    "torque_label": "Current Torque",
    "alarm_title": "Alarm Information",
    "alarm_status": "Status",
    "alarm_type": "Type",
    "alarm_level": "Level",
    "alarm_time": "Time",
    "alarm_depth": "Depth",
    "depth_value_format": "{:.2f} m",
    "speed_value_format": "{:.2f} m/h",
    "tension_value_format": "{:.2f} kN",
    "torque_value_format": "{:.1f} N·m",
    
    # 状态更新相关
    "error_update": "Error updating display: {}",
    
    # 报警时间和深度
    "alarm_time": "03:49:10",
    "alarm_depth": "2427m",
    
    # 图表相关
    "plot_styles": {
        "speed": "Speed Curve",
        "tension": "Tension Curve",
        "acceleration": "Acceleration Curve",
        "diff_tension": "Differential Tension Curve"
    },
    
    # 报警相关补充
    "alarm_types": {
        "device_stuck": "Device Stuck",
        "tension_abnormal": "Tension Abnormal",
        "speed_fluctuation": "Speed Fluctuation"
    },
    "alarm_icons": {
        "warning": "⚠",
        "stuck": "⛔",
        "tension": "⚡",
        "speed": "📊"
    },
    "export_records": "Export Records",
    
    # 状态和控制相关补充
    "status_display": {
        "normal": "Normal Operation",
        "warning": "Warning State",
        "error": "Error State",
        "offline": "Offline State"
    },
    
    # 图表和数据相关补充
    "chart_axis": {
        "time": "Time",
        "speed": "Speed",
        "tension": "Tension",
        "acceleration": "Acceleration"
    },
    
    # 操作控制器相关文本
    "controller": {
        # 按钮状态
        "connect_winch": "Connect Winch",
        "start": "Start",
        "disconnect_winch": "Disconnect Winch",
        "lift_up": "Lift Up",
        "stop_lift": "Stop Lifting",
        "lower_down": "Lower Down",
        "stop_lower": "Stop Lowering",
        
        # 设备状态
        "device_running": "Device Running",
        "device_stopped": "Device Stopped",
        "next_lower": "Device Lowering",
        
        # 操作提示
        "operation_tip": "Operation Tip",
        "stop_lift_msg": "Device stopped lifting",
        "stop_lower_msg": "Device stopped lowering",
        
        # 数据源配置
        "data_source": {
            "acme_device": "ACME Device",
            "csv_file": "CSV File",
            "network_conn": "Network Connection",
            "depth_curve": "Depth Curve Name",
            "speed_curve": "Speed Curve Name",
            "tension_curve": "Tension Curve Name",
            "csv_config": "CSV File Configuration",
            "file_path": "CSV File Path",
            "browse": "Browse...",
            "depth_col": "Depth Column Index",
            "speed_col": "Speed Column Index",
            "tension_col": "Tension Column Index",
            "network_config": "Network Configuration",
            "data_format": "Data Format",
            "connect": "Connect",
            "cancel": "Cancel"
        },
        
        # 错误和警告
        "load_schedule_error": "Failed to load schedule file: {}",
        "schedule_load_failed": "Schedule data loading failed",
        "command_failed": "Command sending failed",
        "check_connection": "Communication with device failed, please check connection",
        
        # 按钮样式
        "button_styles": {
            "normal": {
                "bg_color": "#28a745",
                "hover_color": "#218838"
            },
            "warning": {
                "bg_color": "#dc3545",
                "hover_color": "#c82333"
            },
            "disabled": {
                "bg_color": "#cccccc",
                "text_color": "#666666"
            }
        }
    },
    
    # 排程视图相关文本
    "schedule": {
        # 标题和标签
        "schedule_file_list": "Schedule File List",
        "jc_data_files": "JC Data Files",
        "search_label": "Search Files:",
        "search_placeholder": "Enter keywords to search...",
        "current_file": "Current File: {} ({}rows x {}columns)",
        
        # 按钮文本
        "upload_schedule": "Upload Schedule File",
        "delete_file": "Delete File",
        "export_file": "Export File",
        "refresh_data": "Refresh Data",
        "save_changes": "Save Changes",
        "download_selected": "Download Selected",
        "delete_selected": "Delete Selected",
        
        # 表格列标题
        "table_headers": {
            "run_to": "Run To",
            "ref_depth": "Reference Depth",
            "run_speed": "Run Speed",
            "record": "Record",
            "next_action": "Next Action",
            "run_status": "Run Status"
        },
        
        # 状态文本
        "status": {
            "normal": "Normal",
            "warning": "Warning",
            "error": "Error",
            "no": "No",
            "yes": "Yes",
            "lower": "Lower",
            "lift": "Lift",
            "stop": "Stop"
        },
        
        # 错误和提示消息
        "messages": {
            "display_error": "Cannot display data: {}",
            "delete_confirm": "Confirm Deletion",
            "delete_confirm_msg": "Are you sure you want to delete the selected files?",
            "delete_success": "Deletion Successful",
            "delete_error": "Deletion Failed: {}",
            "save_success": "Save Successful",
            "save_error": "Save Failed: {}",
            "no_file_selected": "Please select a file first",
            "download_success": "Download Successful",
            "download_error": "Download Failed: {}"
        },
        
        # 文件相关
        "file": {
            "page_info": "Page {} / {} Total",
            "page_error": "Page Error",
            "page_range_error": "Please enter a page number between 1 and {}",
            "invalid_page": "Please enter a valid number",
            "upload_title": "Upload File",
            "upload_status": "Connecting to server...",
            "upload_ready": "Connection successful, ready to upload...",
            "upload_progress": "Uploading: {}% ({}/{} bytes)",
            "upload_complete": "Upload Complete!",
            "upload_success": "File {} has been successfully uploaded to server",
            "upload_cancelled": "Upload Cancelled",
            "upload_error": "Upload Error: {}",
            "upload_failed": "Upload Failed: {}"
        },
        
        # 右键菜单
        "context_menu": {
            "toggle_select": "Toggle Selection",
            "view_content": "View File Content",
            "select_file": "Select File",
            "deselect_file": "Deselect File"
        },
        
        # 错误提示
        "errors": {
            "display_error": "Display Error: {}",
            "upload_error": "Upload Error: {}"
        }
    },
    
    # 设备视图相关文本
    "device": {
        # 标题和标签
        "device_info": "Device Information",
        "device_list": "Device List",
        "device_status": "Device Status",
        "device_params": "Device Parameters",
        "device_control": "Device Control",
        
        # 设备信息标签
        "device_id": "Device ID",
        "device_name": "Device Name",
        "device_type": "Device Type",
        "device_model": "Device Model",
        "serial_number": "Serial Number",
        "manufacture_date": "Manufacture Date",
        "last_maintenance": "Last Maintenance",
        "next_maintenance": "Next Maintenance",
        
        # 状态信息
        "status": {
            "online": "Online",
            "offline": "Offline",
            "running": "Running",
            "stopped": "Stopped",
            "error": "Error",
            "maintenance": "Maintenance",
            "normal": "Normal Operation"
        },
        
        # 参数设置
        "params": {
            "speed_limit": "Speed Limit",
            "tension_limit": "Tension Limit",
            "acceleration_limit": "Acceleration Limit",
            "max_depth": "Maximum Depth",
            "unit_speed": "m/h",
            "unit_tension": "kN",
            "unit_acceleration": "m/s²",
            "unit_depth": "m"
        },
        
        # 按钮文本
        "buttons": {
            "add_device": "Add Device",
            "remove_device": "Remove Device",
            "edit_device": "Edit Device",
            "save_params": "Save Parameters",
            "reset_params": "Reset Parameters",
            "start_maintenance": "Start Maintenance",
            "end_maintenance": "End Maintenance",
            "refresh": "Refresh"
        },
        
        # 对话框文本
        "dialogs": {
            "add_title": "Add New Device",
            "edit_title": "Edit Device Information",
            "remove_title": "Remove Device",
            "remove_confirm": "Are you sure you want to remove this device?",
            "save_success": "Save Successful",
            "save_error": "Save Failed: {}",
            "connection_error": "Connection Error: {}"
        },
        
        # 提示信息
        "tooltips": {
            "speed_limit": "Set maximum speed for device operation",
            "tension_limit": "Set maximum tension allowed for the device",
            "acceleration_limit": "Set maximum acceleration for the device",
            "max_depth": "Set maximum working depth for the device"
        },
        
        # 错误消息
        "errors": {
            "load_failed": "Failed to load device information: {}",
            "save_failed": "Failed to save device information: {}",
            "connect_failed": "Device connection failed: {}",
            "invalid_param": "Invalid parameter value: {}"
        },
        
        # 表格标题
        "table_headers": {
            "param": ["Parameter Name", "Parameter Value", "Unit"],
            "maintenance": ["Maintenance Item", "Last Maintenance Time", "Maintenance Cycle", "Next Maintenance Time"],
            "well": ["Well Number", "Well Depth(m)", "Status", "Latest Operation Time", "Remarks"],
            "stats": ["Status Type", "Number of Wells", "Percentage"]
        },
        
        # 分组标题
        "groups": {
            "basic_params": "Basic Parameters",
            "maintenance_params": "Maintenance Parameters",
            "well_info": "Well Information",
            "well_stats": "Well Statistics"
        },
        
        # 标签页标题
        "tabs": {
            "basic": "Basic Parameters",
            "maintenance": "Maintenance Info",
            "well_list": "Well List",
            "stats": "Statistics"
        },
        
        # 井号相关按钮
        "well_buttons": {
            "add": "Add Well",
            "edit": "Edit Well",
            "delete": "Delete Well"
        },
        
        # 状态文本
        "status_text": {
            "normal": "Normal",
            "maintenance": "In Maintenance",
            "repair": "Under Repair"
        },
        
        # 样式配置
        "styles": {
            "colors": {
                "normal": "#4caf50",
                "warning": "#ff9800",
                "info": "#2196f3",
                "error": "#f44336"
            },
            "status_indicator": {
                "normal": "background-color: #4caf50; color: white; padding: 3px 8px; border-radius: 4px;"
            }
        }
    }
} 