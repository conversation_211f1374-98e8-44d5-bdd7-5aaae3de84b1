# -*- coding: utf-8 -*-
"""
国际化管理器
负责管理多语言文本和语言切换
"""

# 标准库
import os
from typing import Dict, Any

# 第三方库
from PySide6.QtCore import QObject, Signal

# 本地库
from common.i18n.zh_CN import TEXTS as ZH_CN_TEXTS
from common.i18n.en_US import TEXTS as EN_US_TEXTS


class I18nManager(QObject):
    """国际化管理器"""
    
    # 语言变更信号
    language_changed = Signal(str)  # 语言代码
    
    def __init__(self):
        super().__init__()
        self._current_language = "zh_CN"
        self._languages = {
            "zh_CN": {
                "name": "中文",
                "display_name": "🇨🇳 中文",
                "texts": ZH_CN_TEXTS
            },
            "en_US": {
                "name": "English", 
                "display_name": "🇺🇸 English",
                "texts": EN_US_TEXTS
            }
        }
    
    def get_current_language(self) -> str:
        """获取当前语言代码"""
        return self._current_language
    
    def set_language(self, language_code: str):
        """设置语言"""
        if language_code in self._languages:
            self._current_language = language_code
            self.language_changed.emit(language_code)
    
    def get_text(self, key: str, default: str = "") -> str:
        """获取文本"""
        language = self._languages.get(self._current_language, self._languages["zh_CN"])
        texts = language["texts"]
        
        # 支持嵌套键，如 "controller.connect_winch"
        keys = key.split(".")
        current = texts
        
        try:
            for k in keys:
                current = current[k]
            return str(current)
        except (KeyError, TypeError):
            return default or key
    
    def get_available_languages(self) -> Dict[str, str]:
        """获取可用语言列表"""
        return {code: lang["display_name"] for code, lang in self._languages.items()}
    
    def get_language_name(self, language_code: str = None) -> str:
        """获取语言名称"""
        if language_code is None:
            language_code = self._current_language
        language = self._languages.get(language_code, self._languages["zh_CN"])
        return language["name"]
    
    def format_text(self, key: str, *args, **kwargs) -> str:
        """格式化文本"""
        text = self.get_text(key)
        try:
            return text.format(*args, **kwargs)
        except (KeyError, ValueError):
            return text


# 全局国际化管理器实例
i18n_manager = I18nManager()

# 便捷函数
def tr(key: str, default: str = "") -> str:
    """获取翻译文本的便捷函数"""
    return i18n_manager.get_text(key, default)
